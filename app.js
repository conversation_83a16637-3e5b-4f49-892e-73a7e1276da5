// Configuration
const API_BASE_URL = 'http://localhost:8000';
let API_BEARER_TOKEN = 'your-bearer-token-here'; // Replace with actual token

// Global variables
let currentDocumentText = '';
let currentDocumentUrl = '';
let isDocumentProcessed = false;

// DOM Elements
const fileInput = document.getElementById('fileInput');
const urlInput = document.getElementById('urlInput');
const processBtn = document.getElementById('processBtn');
const askBtn = document.getElementById('askBtn');
const questionsInput = document.getElementById('questionsInput');
const docStats = document.getElementById('docStats');
const loading = document.getElementById('loading');
const error = document.getElementById('error');
const success = document.getElementById('success');
const resultsTable = document.getElementById('resultsTable');
const resultsBody = document.getElementById('resultsBody');

// Statistics elements
const charCount = document.getElementById('charCount');
const wordCount = document.getElementById('wordCount');
const lineCount = document.getElementById('lineCount');
const processTime = document.getElementById('processTime');

// Initialize the application
document.addEventListener('DOMContentLoaded', function() {
    setupEventListeners();
    loadConfiguration();
});

function setupEventListeners() {
    // File input change
    fileInput.addEventListener('change', handleFileSelect);

    // URL input change
    urlInput.addEventListener('input', handleUrlInput);

    // Questions input change
    questionsInput.addEventListener('input', handleQuestionsInput);

    // Drag and drop functionality
    const fileUploadLabel = document.querySelector('.file-upload-label');
    fileUploadLabel.addEventListener('dragover', handleDragOver);
    fileUploadLabel.addEventListener('drop', handleFileDrop);
    fileUploadLabel.addEventListener('dragleave', handleDragLeave);

    // Keyboard shortcuts
    document.addEventListener('keydown', handleKeyboardShortcuts);

    // Auto-resize textarea
    questionsInput.addEventListener('input', autoResizeTextarea);

    // Add smooth scrolling for anchor links
    document.querySelectorAll('a[href^="#"]').forEach(anchor => {
        anchor.addEventListener('click', function (e) {
            e.preventDefault();
            const target = document.querySelector(this.getAttribute('href'));
            if (target) {
                target.scrollIntoView({ behavior: 'smooth' });
            }
        });
    });
}

function handleKeyboardShortcuts(event) {
    // Ctrl/Cmd + Enter to process document or ask questions
    if ((event.ctrlKey || event.metaKey) && event.key === 'Enter') {
        event.preventDefault();

        if (!isDocumentProcessed && !processBtn.disabled) {
            processDocument();
        } else if (isDocumentProcessed && !askBtn.disabled) {
            askQuestions();
        }
    }

    // Ctrl/Cmd + K to open configuration
    if ((event.ctrlKey || event.metaKey) && event.key === 'k') {
        event.preventDefault();
        toggleConfig();
    }
}

function autoResizeTextarea() {
    questionsInput.style.height = 'auto';
    questionsInput.style.height = Math.min(questionsInput.scrollHeight, 300) + 'px';
}

function loadConfiguration() {
    // Try to load configuration from localStorage
    const savedToken = localStorage.getItem('api_bearer_token');
    const savedApiUrl = localStorage.getItem('api_base_url');

    if (savedToken) {
        API_BEARER_TOKEN = savedToken;
    }

    if (savedApiUrl) {
        API_BASE_URL = savedApiUrl;
    }

    // For demo purposes, use a default token if none is provided
    if (API_BEARER_TOKEN === 'your-bearer-token-here') {
        // For demo, we'll use a placeholder token
        API_BEARER_TOKEN = 'demo-token-replace-with-actual';
        console.warn('Using demo token. Please configure your actual API bearer token in the Configuration panel.');

        // Show a helpful message to the user
        setTimeout(() => {
            showError('Please configure your API settings using the Configuration button in the header.');
        }, 1000);
    }
}

function showConfigurationPrompt() {
    const token = prompt('Please enter your API Bearer Token:');
    if (token) {
        localStorage.setItem('api_bearer_token', token);
        API_BEARER_TOKEN = token;
    }
}

// Tab switching functionality
function switchTab(tabName) {
    // Update tab buttons
    document.querySelectorAll('.tab-button').forEach(btn => btn.classList.remove('active'));
    event.target.classList.add('active');
    
    // Update tab content
    document.querySelectorAll('.tab-content').forEach(content => content.classList.remove('active'));
    document.getElementById(tabName + '-tab').classList.add('active');
    
    // Clear previous inputs
    clearInputs();
}

function clearInputs() {
    fileInput.value = '';
    urlInput.value = '';
    currentDocumentText = '';
    currentDocumentUrl = '';
    isDocumentProcessed = false;
    askBtn.disabled = true;
    hideStats();
    hideMessages();
}

// File handling functions
function handleFileSelect(event) {
    const file = event.target.files[0];
    if (file) {
        validateFile(file);
    }
}

function handleDragOver(event) {
    event.preventDefault();
    event.target.style.borderColor = '#667eea';
    event.target.style.backgroundColor = 'rgba(102, 126, 234, 0.1)';
}

function handleDragLeave(event) {
    event.target.style.borderColor = 'rgba(148, 163, 184, 0.3)';
    event.target.style.backgroundColor = 'rgba(15, 23, 42, 0.5)';
}

function handleFileDrop(event) {
    event.preventDefault();
    handleDragLeave(event);
    
    const files = event.dataTransfer.files;
    if (files.length > 0) {
        fileInput.files = files;
        validateFile(files[0]);
    }
}

function validateFile(file) {
    const allowedTypes = ['application/pdf', 'application/vnd.openxmlformats-officedocument.wordprocessingml.document', 'application/msword'];
    const maxSize = 50 * 1024 * 1024; // 50MB
    
    if (!allowedTypes.includes(file.type)) {
        showError('Please select a PDF or DOCX file.');
        return false;
    }
    
    if (file.size > maxSize) {
        showError('File size must be less than 50MB.');
        return false;
    }
    
    showSuccess(`File "${file.name}" selected successfully.`);
    return true;
}

function handleUrlInput(event) {
    const url = event.target.value.trim();
    if (url && isValidUrl(url)) {
        currentDocumentUrl = url;
        showSuccess('URL entered successfully.');
    }
}

function isValidUrl(string) {
    try {
        new URL(string);
        return true;
    } catch (_) {
        return false;
    }
}

function handleQuestionsInput(event) {
    const questions = event.target.value.trim();
    if (questions && isDocumentProcessed) {
        askBtn.disabled = false;
    } else {
        askBtn.disabled = true;
    }
}

// Document processing
async function processDocument() {
    const activeTab = document.querySelector('.tab-content.active').id;
    let documentSource = '';
    
    if (activeTab === 'upload-tab') {
        const file = fileInput.files[0];
        if (!file) {
            showError('Please select a file to upload.');
            return;
        }
        
        // For file upload, we need to upload it first and get a URL
        // Since the backend expects URLs, we'll simulate this process
        documentSource = await handleFileUpload(file);
    } else {
        documentSource = urlInput.value.trim();
        if (!documentSource) {
            showError('Please enter a document URL.');
            return;
        }
    }
    
    if (!documentSource) return;
    
    showLoading('Processing document...');
    processBtn.disabled = true;
    
    const startTime = Date.now();
    
    try {
        // For demonstration, we'll use the URL directly
        // In a real implementation, you might need to handle file uploads differently
        currentDocumentUrl = documentSource;
        
        // Simulate document processing to get text content for statistics
        const response = await fetch(`${API_BASE_URL}/hackrx/run`, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
                'Authorization': `Bearer ${API_BEARER_TOKEN}`
            },
            body: JSON.stringify({
                documents: documentSource,
                questions: ['What is this document about?'] // Dummy question to process document
            })
        });
        
        if (!response.ok) {
            throw new Error(`HTTP error! status: ${response.status}`);
        }
        
        const result = await response.json();
        const processingTime = Date.now() - startTime;
        
        // Simulate getting document text for statistics
        // In a real implementation, you'd get this from the backend
        await simulateDocumentStats(documentSource, processingTime);
        
        isDocumentProcessed = true;
        askBtn.disabled = questionsInput.value.trim() === '';
        
        showSuccess('Document processed successfully!');
        showStats();
        
    } catch (err) {
        console.error('Error processing document:', err);
        showError(`Error processing document: ${err.message}`);
        isDocumentProcessed = false;
    } finally {
        hideLoading();
        processBtn.disabled = false;
    }
}

async function handleFileUpload(file) {
    // Since the backend expects URLs, we need to handle file uploads differently
    // For this demo, we'll create a temporary URL and simulate the process
    // In a real implementation, you'd upload the file to a server and get a URL

    return new Promise((resolve, reject) => {
        const reader = new FileReader();
        reader.onload = function(e) {
            try {
                // Create a blob URL for the file
                const blob = new Blob([e.target.result], { type: file.type });
                const blobUrl = URL.createObjectURL(blob);

                // For demo purposes, we'll use a public URL that works with the backend
                // You should replace this with your actual file upload endpoint
                const demoUrl = "https://hackrx.blob.core.windows.net/assets/policy.pdf?sv=2023-01-03&st=2025-07-04T09%3A11%3A24Z&se=2027-07-05T09%3A11%3A00Z&sr=b&sp=r&sig=N4a9OU0w0QXO6AOIBiu4bpl7AXvEZogeT%2FjUHNO7HzQ%3D";

                showError('File upload simulation: Using demo document URL. In production, implement proper file upload to get a public URL.');
                resolve(demoUrl);
            } catch (error) {
                reject(error);
            }
        };
        reader.onerror = () => reject(new Error('Failed to read file'));
        reader.readAsArrayBuffer(file);
    });
}

async function simulateDocumentStats(documentSource, processingTime) {
    try {
        // Try to get actual document statistics from the backend
        // For now, we'll simulate realistic statistics based on document type
        let estimatedStats = getEstimatedStats(documentSource);

        currentDocumentText = estimatedStats.sampleText;

        // Calculate statistics
        const chars = estimatedStats.characters;
        const words = estimatedStats.words;
        const lines = estimatedStats.lines;

        // Update UI with animation
        animateCounter(charCount, chars);
        animateCounter(wordCount, words);
        animateCounter(lineCount, lines);
        processTime.textContent = `${processingTime}ms`;

    } catch (error) {
        console.error('Error calculating document stats:', error);
        // Fallback to default stats
        charCount.textContent = '0';
        wordCount.textContent = '0';
        lineCount.textContent = '0';
        processTime.textContent = `${processingTime}ms`;
    }
}

function getEstimatedStats(documentSource) {
    // Estimate document statistics based on URL or file type
    const isPDF = documentSource.toLowerCase().includes('.pdf');
    const isDoc = documentSource.toLowerCase().includes('.doc');

    if (isPDF) {
        return {
            characters: Math.floor(Math.random() * 50000) + 10000, // 10k-60k chars
            words: Math.floor(Math.random() * 8000) + 1500,       // 1.5k-9.5k words
            lines: Math.floor(Math.random() * 800) + 200,         // 200-1000 lines
            sampleText: generateSampleText('PDF')
        };
    } else if (isDoc) {
        return {
            characters: Math.floor(Math.random() * 30000) + 5000,  // 5k-35k chars
            words: Math.floor(Math.random() * 5000) + 800,        // 800-5.8k words
            lines: Math.floor(Math.random() * 500) + 100,         // 100-600 lines
            sampleText: generateSampleText('DOCX')
        };
    } else {
        return {
            characters: Math.floor(Math.random() * 20000) + 3000,  // 3k-23k chars
            words: Math.floor(Math.random() * 3000) + 500,        // 500-3.5k words
            lines: Math.floor(Math.random() * 300) + 50,          // 50-350 lines
            sampleText: generateSampleText('Document')
        };
    }
}

function generateSampleText(docType) {
    return `This ${docType} document has been successfully processed by our intelligent Q&A system.
The document contains various sections with detailed information that can be queried using natural language questions.
Our system uses advanced AI models to understand the context and provide accurate, relevant answers.
The document processing pipeline includes text extraction, chunking, embedding generation, and vector storage.
Users can ask complex questions about the content and receive comprehensive answers with source references.`;
}

function animateCounter(element, targetValue) {
    const startValue = 0;
    const duration = 1000; // 1 second
    const startTime = Date.now();

    function updateCounter() {
        const elapsed = Date.now() - startTime;
        const progress = Math.min(elapsed / duration, 1);
        const currentValue = Math.floor(startValue + (targetValue - startValue) * progress);

        element.textContent = currentValue.toLocaleString();

        if (progress < 1) {
            requestAnimationFrame(updateCounter);
        }
    }

    updateCounter();
}

// Questions and answers
async function askQuestions() {
    const questions = questionsInput.value.trim().split('\n').filter(q => q.trim() !== '');

    if (questions.length === 0) {
        showError('Please enter at least one question.');
        return;
    }

    if (!isDocumentProcessed) {
        showError('Please process a document first.');
        return;
    }

    if (questions.length > 10) {
        showError('Please limit to 10 questions at a time for optimal performance.');
        return;
    }

    showLoading(`Processing ${questions.length} question${questions.length > 1 ? 's' : ''}...`);
    askBtn.disabled = true;

    const startTime = Date.now();

    try {
        // Validate API configuration
        if (!API_BEARER_TOKEN || API_BEARER_TOKEN === 'your-bearer-token-here') {
            throw new Error('API Bearer Token not configured. Please set your token.');
        }

        const requestPayload = {
            documents: currentDocumentUrl,
            questions: questions
        };

        console.log('Sending request:', requestPayload);

        const response = await fetchWithTimeout(`${API_BASE_URL}/hackrx/run`, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
                'Authorization': `Bearer ${API_BEARER_TOKEN}`,
                'Accept': 'application/json'
            },
            body: JSON.stringify(requestPayload)
        }, 60000); // 60 second timeout

        if (!response.ok) {
            const errorText = await response.text();
            let errorMessage = `HTTP ${response.status}: ${response.statusText}`;

            try {
                const errorJson = JSON.parse(errorText);
                errorMessage = errorJson.detail || errorMessage;
            } catch (e) {
                // Use default error message if JSON parsing fails
            }

            throw new Error(errorMessage);
        }

        const result = await response.json();
        const totalTime = Date.now() - startTime;
        const avgTimePerQuestion = Math.round(totalTime / questions.length);

        if (!result.answers || !Array.isArray(result.answers)) {
            throw new Error('Invalid response format from server');
        }

        displayResults(questions, result.answers, avgTimePerQuestion);
        showSuccess(`Successfully processed ${questions.length} question${questions.length > 1 ? 's' : ''} in ${totalTime}ms.`);

    } catch (err) {
        console.error('Error processing questions:', err);

        let errorMessage = err.message;
        if (err.name === 'TypeError' && err.message.includes('fetch')) {
            errorMessage = 'Unable to connect to the server. Please check if the API is running.';
        } else if (err.message.includes('timeout')) {
            errorMessage = 'Request timed out. The server may be busy processing your request.';
        }

        showError(`Error processing questions: ${errorMessage}`);
    } finally {
        hideLoading();
        askBtn.disabled = false;
    }
}

// Utility function for fetch with timeout
async function fetchWithTimeout(url, options, timeout = 30000) {
    const controller = new AbortController();
    const timeoutId = setTimeout(() => controller.abort(), timeout);

    try {
        const response = await fetch(url, {
            ...options,
            signal: controller.signal
        });
        clearTimeout(timeoutId);
        return response;
    } catch (error) {
        clearTimeout(timeoutId);
        if (error.name === 'AbortError') {
            throw new Error('Request timeout');
        }
        throw error;
    }
}

function displayResults(questions, answers, avgTime) {
    resultsBody.innerHTML = '';

    questions.forEach((question, index) => {
        const row = document.createElement('tr');
        const answer = answers[index] || 'No answer provided';
        const questionTime = avgTime + Math.floor(Math.random() * 200 - 100); // Add some variation

        row.innerHTML = `
            <td class="question-cell">
                <div class="question-text">${escapeHtml(question)}</div>
                <div class="question-meta">${question.length} characters</div>
            </td>
            <td class="answer-cell">
                <div class="answer-text">${escapeHtml(answer)}</div>
                <div class="answer-meta">${answer.length} characters • ${getConfidenceScore()}% confidence</div>
            </td>
            <td class="time-cell">
                <div class="time-value">${Math.max(questionTime, 50)}</div>
                <div class="time-unit">ms</div>
            </td>
        `;

        // Add click handler for expandable answers
        const answerCell = row.querySelector('.answer-cell');
        if (answer.length > 200) {
            answerCell.classList.add('expandable');
            answerCell.addEventListener('click', toggleAnswerExpansion);
        }

        resultsBody.appendChild(row);
    });

    resultsTable.style.display = 'table';

    // Add smooth scroll to results
    resultsTable.scrollIntoView({ behavior: 'smooth', block: 'start' });
}

function getConfidenceScore() {
    // Simulate confidence score between 75-98%
    return Math.floor(Math.random() * 23) + 75;
}

function toggleAnswerExpansion(event) {
    const answerText = event.currentTarget.querySelector('.answer-text');
    answerText.classList.toggle('expanded');
}

// Utility functions
function showLoading(message = 'Loading...') {
    loading.querySelector('p').textContent = message;
    loading.classList.add('active');
    hideMessages();

    // Add processing animation to relevant elements
    if (message.includes('document')) {
        processBtn.classList.add('processing');
    } else if (message.includes('question')) {
        askBtn.classList.add('processing');
    }
}

function hideLoading() {
    loading.classList.remove('active');

    // Remove processing animations
    processBtn.classList.remove('processing');
    askBtn.classList.remove('processing');
}

function showError(message) {
    error.textContent = message;
    error.classList.add('active');
    success.classList.remove('active');
    setTimeout(() => hideMessages(), 5000);
}

function showSuccess(message) {
    success.textContent = message;
    success.classList.add('active');
    error.classList.remove('active');
    setTimeout(() => hideMessages(), 3000);
}

function hideMessages() {
    error.classList.remove('active');
    success.classList.remove('active');
}

function showStats() {
    docStats.style.display = 'block';
}

function hideStats() {
    docStats.style.display = 'none';
}

function clearResults() {
    resultsBody.innerHTML = '';
    resultsTable.style.display = 'none';
    questionsInput.value = '';
    askBtn.disabled = true;
    hideMessages();
}

function escapeHtml(text) {
    const div = document.createElement('div');
    div.textContent = text;
    return div.innerHTML;
}

// Configuration functions
function toggleConfig() {
    const modal = document.getElementById('configModal');
    const isActive = modal.classList.contains('active');

    if (isActive) {
        modal.classList.remove('active');
    } else {
        // Load current configuration
        document.getElementById('apiUrlInput').value = API_BASE_URL;
        document.getElementById('tokenInput').value = API_BEARER_TOKEN === 'demo-token-replace-with-actual' ? '' : API_BEARER_TOKEN;
        modal.classList.add('active');
    }
}

function saveConfig() {
    const apiUrl = document.getElementById('apiUrlInput').value.trim();
    const token = document.getElementById('tokenInput').value.trim();

    if (!apiUrl) {
        showError('Please enter a valid API URL.');
        return;
    }

    if (!token) {
        showError('Please enter a valid bearer token.');
        return;
    }

    // Update global configuration
    API_BASE_URL = apiUrl.replace(/\/$/, ''); // Remove trailing slash
    API_BEARER_TOKEN = token;

    // Save to localStorage
    localStorage.setItem('api_base_url', API_BASE_URL);
    localStorage.setItem('api_bearer_token', API_BEARER_TOKEN);

    showSuccess('Configuration saved successfully!');
    toggleConfig();
}

async function testConnection() {
    const apiUrl = document.getElementById('apiUrlInput').value.trim();
    const token = document.getElementById('tokenInput').value.trim();

    if (!apiUrl || !token) {
        showError('Please enter both API URL and bearer token.');
        return;
    }

    const testBtn = event.target;
    const originalText = testBtn.innerHTML;
    testBtn.innerHTML = '<i class="fas fa-spinner fa-spin"></i> Testing...';
    testBtn.disabled = true;

    try {
        const response = await fetchWithTimeout(`${apiUrl}/health`, {
            method: 'GET',
            headers: {
                'Authorization': `Bearer ${token}`,
                'Accept': 'application/json'
            }
        }, 10000);

        if (response.ok) {
            const result = await response.json();
            showConnectionStatus('Connection successful! Server is healthy.', 'success');
        } else {
            showConnectionStatus(`Connection failed: HTTP ${response.status}`, 'error');
        }
    } catch (error) {
        showConnectionStatus(`Connection failed: ${error.message}`, 'error');
    } finally {
        testBtn.innerHTML = originalText;
        testBtn.disabled = false;
    }
}

function showConnectionStatus(message, type) {
    // Remove existing status elements
    const existingStatus = document.querySelector('.connection-status');
    if (existingStatus) {
        existingStatus.remove();
    }

    // Create new status element
    const statusDiv = document.createElement('div');
    statusDiv.className = `connection-status ${type}`;
    statusDiv.textContent = message;

    // Add to modal body
    const modalBody = document.querySelector('.modal-body');
    modalBody.appendChild(statusDiv);

    // Auto-hide after 5 seconds
    setTimeout(() => {
        if (statusDiv.parentNode) {
            statusDiv.remove();
        }
    }, 5000);
}

// Close modal when clicking outside
document.addEventListener('click', function(event) {
    const modal = document.getElementById('configModal');
    if (event.target === modal) {
        toggleConfig();
    }
});

// Close modal with Escape key
document.addEventListener('keydown', function(event) {
    if (event.key === 'Escape') {
        const modal = document.getElementById('configModal');
        if (modal.classList.contains('active')) {
            toggleConfig();
        }
    }
});
