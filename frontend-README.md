# Document Q&A Frontend

A modern, responsive web interface for the Document Q&A System that allows users to upload documents, provide URLs, ask questions, and receive intelligent answers.

## Features

### 🎨 Modern Dark UI
- Clean, dark theme with gradient backgrounds
- Responsive design that works on all devices
- Smooth animations and transitions
- Interactive elements with hover effects

### 📄 Document Processing
- **File Upload**: Drag & drop or click to upload PDF/DOCX files
- **URL Input**: Provide direct links to documents
- **Real-time Statistics**: Shows character count, word count, line count, and processing time
- **Format Validation**: Ensures only supported file types are processed

### ❓ Question & Answer Interface
- **Multi-line Questions**: Enter multiple questions separated by new lines
- **Real-time Answers**: Get intelligent responses with timing information
- **Expandable Answers**: Long answers can be expanded/collapsed
- **Confidence Scores**: Shows AI confidence levels for each answer

### ⚙️ Configuration Panel
- **API Settings**: Configure API base URL and bearer token
- **Connection Testing**: Test API connectivity before use
- **Persistent Storage**: Settings saved in browser localStorage

### 🚀 User Experience
- **Keyboard Shortcuts**: 
  - `Ctrl/Cmd + Enter`: Process document or ask questions
  - `Ctrl/Cmd + K`: Open configuration panel
  - `Escape`: Close modals
- **Auto-resize**: Text areas automatically adjust to content
- **Loading States**: Visual feedback during processing
- **Error Handling**: Clear error messages and recovery suggestions

## Setup Instructions

### 1. Prerequisites
- Ensure your FastAPI backend is running (default: `http://localhost:8000`)
- Have a valid API bearer token for authentication

### 2. Configuration
1. Open `index.html` in a web browser
2. Click the "Configuration" button in the header
3. Enter your API settings:
   - **API Base URL**: Your backend server URL (e.g., `http://localhost:8000`)
   - **Bearer Token**: Your authentication token
4. Click "Test Connection" to verify settings
5. Click "Save Configuration" to store settings

### 3. Using the Interface

#### Document Processing
1. **Upload Method**: 
   - Switch to "Upload File" tab
   - Drag & drop a PDF/DOCX file or click to browse
   - Click "Process Document"

2. **URL Method**:
   - Switch to "URL Input" tab  
   - Enter a direct link to a PDF/DOCX document
   - Click "Process Document"

3. **View Statistics**: After processing, see document statistics including character count, word count, lines, and processing time

#### Asking Questions
1. Enter your questions in the text area (one per line)
2. Click "Ask Questions" or press `Ctrl/Cmd + Enter`
3. View answers in the results table with timing information
4. Click on long answers to expand/collapse them

## File Structure

```
├── index.html          # Main HTML file with UI structure
├── app.js             # JavaScript functionality and API integration
└── frontend-README.md # This documentation file
```

## Browser Compatibility

- **Modern Browsers**: Chrome 80+, Firefox 75+, Safari 13+, Edge 80+
- **Mobile**: Responsive design works on iOS Safari and Android Chrome
- **Features Used**: ES6+, Fetch API, CSS Grid, Flexbox, CSS Custom Properties

## Customization

### Styling
- Colors and themes can be modified in the CSS section of `index.html`
- The design uses CSS custom properties for easy theme customization
- Responsive breakpoints: 768px (tablet), 480px (mobile)

### API Integration
- API endpoints can be modified in `app.js`
- Request/response handling can be customized for different backend formats
- Authentication method can be changed (currently uses Bearer token)

## Troubleshooting

### Common Issues

1. **"Unable to connect to server"**
   - Check if the backend is running
   - Verify the API URL in configuration
   - Check browser console for CORS errors

2. **"Invalid authentication token"**
   - Verify your bearer token is correct
   - Check token expiration
   - Ensure token has proper permissions

3. **File upload not working**
   - Currently uses demo URL for file uploads
   - Implement proper file upload endpoint in backend
   - Check file size limits (50MB max)

4. **Questions not processing**
   - Ensure document is processed first
   - Check question format (one per line)
   - Verify API connectivity

### Development Tips

1. **Testing**: Use browser developer tools to monitor network requests
2. **Debugging**: Check browser console for JavaScript errors
3. **Performance**: Monitor processing times in the statistics panel
4. **Mobile**: Test responsive design on various screen sizes

## Security Notes

- Bearer tokens are stored in browser localStorage
- Use HTTPS in production environments
- Implement proper CORS policies on the backend
- Consider token expiration and refresh mechanisms

## Future Enhancements

- [ ] Batch file upload support
- [ ] Document preview functionality
- [ ] Export answers to PDF/Word
- [ ] Question templates and suggestions
- [ ] Advanced filtering and search in results
- [ ] Real-time collaboration features
- [ ] Integration with cloud storage services

## Support

For issues related to:
- **Frontend**: Check browser console and network tab
- **Backend**: Refer to the main project documentation
- **API**: Test endpoints using the configuration panel

---

**Note**: This frontend is designed to work with the FastAPI backend. Ensure your backend server is running and properly configured before using the interface.
