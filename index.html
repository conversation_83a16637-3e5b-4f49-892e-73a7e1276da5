<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Document Q&A System</title>
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Inter', sans-serif;
            background: linear-gradient(135deg, #0f0f23 0%, #1a1a2e 50%, #16213e 100%);
            color: #e2e8f0;
            min-height: 100vh;
            line-height: 1.6;
        }

        .container {
            max-width: 1400px;
            margin: 0 auto;
            padding: 20px;
        }

        .header {
            text-align: center;
            margin-bottom: 40px;
            padding: 30px 0;
        }

        .header h1 {
            font-size: 3rem;
            font-weight: 700;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
            margin-bottom: 10px;
        }

        .header p {
            font-size: 1.2rem;
            color: #94a3b8;
            font-weight: 300;
        }

        .main-content {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 30px;
            margin-bottom: 40px;
        }

        .card {
            background: rgba(30, 41, 59, 0.8);
            backdrop-filter: blur(10px);
            border: 1px solid rgba(148, 163, 184, 0.1);
            border-radius: 16px;
            padding: 30px;
            box-shadow: 0 20px 25px -5px rgba(0, 0, 0, 0.3);
            transition: all 0.3s ease;
        }

        .card:hover {
            transform: translateY(-2px);
            box-shadow: 0 25px 50px -12px rgba(0, 0, 0, 0.4);
            border-color: rgba(148, 163, 184, 0.2);
        }

        .card-title {
            font-size: 1.5rem;
            font-weight: 600;
            margin-bottom: 20px;
            color: #f1f5f9;
            display: flex;
            align-items: center;
            gap: 10px;
        }

        .card-title i {
            color: #667eea;
        }

        .input-group {
            margin-bottom: 20px;
        }

        .input-label {
            display: block;
            margin-bottom: 8px;
            font-weight: 500;
            color: #cbd5e1;
        }

        .input-field {
            width: 100%;
            padding: 12px 16px;
            background: rgba(15, 23, 42, 0.8);
            border: 1px solid rgba(148, 163, 184, 0.2);
            border-radius: 8px;
            color: #e2e8f0;
            font-size: 14px;
            transition: all 0.3s ease;
        }

        .input-field:focus {
            outline: none;
            border-color: #667eea;
            box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
        }

        .input-field::placeholder {
            color: #64748b;
        }

        .file-upload {
            position: relative;
            display: inline-block;
            width: 100%;
        }

        .file-upload input[type="file"] {
            position: absolute;
            opacity: 0;
            width: 100%;
            height: 100%;
            cursor: pointer;
        }

        .file-upload-label {
            display: flex;
            align-items: center;
            justify-content: center;
            gap: 10px;
            padding: 20px;
            border: 2px dashed rgba(148, 163, 184, 0.3);
            border-radius: 8px;
            background: rgba(15, 23, 42, 0.5);
            cursor: pointer;
            transition: all 0.3s ease;
            text-align: center;
        }

        .file-upload-label:hover {
            border-color: #667eea;
            background: rgba(102, 126, 234, 0.1);
        }

        .file-upload-label i {
            font-size: 1.5rem;
            color: #667eea;
        }

        .textarea {
            min-height: 120px;
            resize: vertical;
            font-family: inherit;
        }

        .btn {
            padding: 12px 24px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            border: none;
            border-radius: 8px;
            font-weight: 500;
            cursor: pointer;
            transition: all 0.3s ease;
            display: inline-flex;
            align-items: center;
            gap: 8px;
            font-size: 14px;
        }

        .btn:hover {
            transform: translateY(-1px);
            box-shadow: 0 10px 20px rgba(102, 126, 234, 0.3);
        }

        .btn:disabled {
            opacity: 0.6;
            cursor: not-allowed;
            transform: none;
        }

        .btn-secondary {
            background: rgba(148, 163, 184, 0.2);
            color: #e2e8f0;
        }

        .btn-secondary:hover {
            background: rgba(148, 163, 184, 0.3);
            box-shadow: 0 10px 20px rgba(148, 163, 184, 0.2);
        }

        .stats-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(120px, 1fr));
            gap: 15px;
            margin-top: 20px;
        }

        .stat-item {
            background: rgba(15, 23, 42, 0.6);
            padding: 15px;
            border-radius: 8px;
            text-align: center;
            border: 1px solid rgba(148, 163, 184, 0.1);
        }

        .stat-value {
            font-size: 1.5rem;
            font-weight: 600;
            color: #667eea;
            display: block;
        }

        .stat-label {
            font-size: 0.8rem;
            color: #94a3b8;
            text-transform: uppercase;
            letter-spacing: 0.5px;
        }

        .results-section {
            grid-column: 1 / -1;
        }

        .results-table {
            width: 100%;
            border-collapse: collapse;
            margin-top: 20px;
        }

        .results-table th,
        .results-table td {
            padding: 15px;
            text-align: left;
            border-bottom: 1px solid rgba(148, 163, 184, 0.1);
        }

        .results-table th {
            background: rgba(15, 23, 42, 0.8);
            font-weight: 600;
            color: #f1f5f9;
        }

        .results-table td {
            background: rgba(30, 41, 59, 0.4);
        }

        .results-table tr:hover td {
            background: rgba(30, 41, 59, 0.6);
        }

        .question-cell, .answer-cell {
            vertical-align: top;
        }

        .question-text, .answer-text {
            margin-bottom: 5px;
            line-height: 1.5;
        }

        .question-meta, .answer-meta {
            font-size: 0.8rem;
            color: #64748b;
            font-style: italic;
        }

        .time-cell {
            text-align: center;
            vertical-align: middle;
        }

        .time-value {
            font-size: 1.2rem;
            font-weight: 600;
            color: #667eea;
        }

        .time-unit {
            font-size: 0.8rem;
            color: #94a3b8;
        }

        .answer-cell.expandable {
            cursor: pointer;
            position: relative;
        }

        .answer-cell.expandable::after {
            content: '▼ Click to expand';
            position: absolute;
            bottom: 5px;
            right: 10px;
            font-size: 0.7rem;
            color: #667eea;
            opacity: 0.7;
        }

        .answer-text {
            max-height: 100px;
            overflow: hidden;
            transition: max-height 0.3s ease;
        }

        .answer-text.expanded {
            max-height: none;
        }

        .answer-cell.expandable .answer-text.expanded + .answer-meta::after {
            content: ' • Click to collapse';
        }

        .loading {
            display: none;
            text-align: center;
            padding: 40px;
        }

        .loading.active {
            display: block;
        }

        .spinner {
            width: 40px;
            height: 40px;
            border: 4px solid rgba(148, 163, 184, 0.2);
            border-left: 4px solid #667eea;
            border-radius: 50%;
            animation: spin 1s linear infinite;
            margin: 0 auto 20px;
        }

        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }

        .error {
            background: rgba(239, 68, 68, 0.1);
            border: 1px solid rgba(239, 68, 68, 0.3);
            color: #fca5a5;
            padding: 15px;
            border-radius: 8px;
            margin-top: 20px;
            display: none;
        }

        .error.active {
            display: block;
        }

        .success {
            background: rgba(34, 197, 94, 0.1);
            border: 1px solid rgba(34, 197, 94, 0.3);
            color: #86efac;
            padding: 15px;
            border-radius: 8px;
            margin-top: 20px;
            display: none;
        }

        .success.active {
            display: block;
        }

        @media (max-width: 768px) {
            .main-content {
                grid-template-columns: 1fr;
                gap: 20px;
            }

            .header h1 {
                font-size: 2rem;
            }

            .header p {
                font-size: 1rem;
            }

            .card {
                padding: 20px;
            }

            .stats-grid {
                grid-template-columns: repeat(2, 1fr);
                gap: 10px;
            }

            .results-table {
                font-size: 0.9rem;
            }

            .results-table th,
            .results-table td {
                padding: 10px 8px;
            }

            .modal-content {
                width: 95%;
                margin: 10px;
            }

            .modal-header,
            .modal-body {
                padding: 20px;
            }

            .tab-buttons {
                flex-direction: column;
                gap: 5px;
            }

            .tab-button {
                text-align: center;
            }
        }

        @media (max-width: 480px) {
            .container {
                padding: 10px;
            }

            .header h1 {
                font-size: 1.5rem;
            }

            .card-title {
                font-size: 1.2rem;
            }

            .stats-grid {
                grid-template-columns: 1fr;
            }

            .btn {
                width: 100%;
                justify-content: center;
                margin-bottom: 10px;
            }

            .results-table {
                display: block;
                overflow-x: auto;
                white-space: nowrap;
            }
        }

        /* Animation enhancements */
        .card {
            animation: fadeInUp 0.6s ease-out;
        }

        .card:nth-child(2) {
            animation-delay: 0.1s;
        }

        .card:nth-child(3) {
            animation-delay: 0.2s;
        }

        @keyframes fadeInUp {
            from {
                opacity: 0;
                transform: translateY(30px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }

        .results-table tr {
            animation: slideInLeft 0.3s ease-out;
        }

        @keyframes slideInLeft {
            from {
                opacity: 0;
                transform: translateX(-20px);
            }
            to {
                opacity: 1;
                transform: translateX(0);
            }
        }

        /* Pulse animation for processing */
        .processing {
            animation: pulse 2s infinite;
        }

        @keyframes pulse {
            0% {
                box-shadow: 0 0 0 0 rgba(102, 126, 234, 0.4);
            }
            70% {
                box-shadow: 0 0 0 10px rgba(102, 126, 234, 0);
            }
            100% {
                box-shadow: 0 0 0 0 rgba(102, 126, 234, 0);
            }
        }

        /* Smooth transitions for all interactive elements */
        * {
            transition: all 0.3s ease;
        }

        /* Focus styles for accessibility */
        .input-field:focus,
        .btn:focus,
        .tab-button:focus {
            outline: 2px solid #667eea;
            outline-offset: 2px;
        }

        /* Loading skeleton animation */
        .skeleton {
            background: linear-gradient(90deg, rgba(148, 163, 184, 0.1) 25%, rgba(148, 163, 184, 0.2) 50%, rgba(148, 163, 184, 0.1) 75%);
            background-size: 200% 100%;
            animation: loading 1.5s infinite;
        }

        @keyframes loading {
            0% {
                background-position: 200% 0;
            }
            100% {
                background-position: -200% 0;
            }
        }

        .tab-buttons {
            display: flex;
            gap: 10px;
            margin-bottom: 20px;
        }

        .tab-button {
            padding: 8px 16px;
            background: rgba(148, 163, 184, 0.1);
            border: 1px solid rgba(148, 163, 184, 0.2);
            border-radius: 6px;
            color: #94a3b8;
            cursor: pointer;
            transition: all 0.3s ease;
            font-size: 14px;
        }

        .tab-button.active {
            background: rgba(102, 126, 234, 0.2);
            border-color: #667eea;
            color: #667eea;
        }

        .tab-content {
            display: none;
        }

        .tab-content.active {
            display: block;
        }

        .config-panel {
            margin-top: 20px;
        }

        .modal {
            display: none;
            position: fixed;
            z-index: 1000;
            left: 0;
            top: 0;
            width: 100%;
            height: 100%;
            background-color: rgba(0, 0, 0, 0.7);
            backdrop-filter: blur(5px);
        }

        .modal.active {
            display: flex;
            align-items: center;
            justify-content: center;
        }

        .modal-content {
            background: rgba(30, 41, 59, 0.95);
            border: 1px solid rgba(148, 163, 184, 0.2);
            border-radius: 16px;
            padding: 0;
            max-width: 500px;
            width: 90%;
            max-height: 80vh;
            overflow-y: auto;
            box-shadow: 0 25px 50px -12px rgba(0, 0, 0, 0.5);
        }

        .modal-header {
            padding: 20px 30px;
            border-bottom: 1px solid rgba(148, 163, 184, 0.1);
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        .modal-header h3 {
            margin: 0;
            color: #f1f5f9;
            display: flex;
            align-items: center;
            gap: 10px;
        }

        .modal-close {
            background: none;
            border: none;
            color: #94a3b8;
            font-size: 1.5rem;
            cursor: pointer;
            padding: 0;
            width: 30px;
            height: 30px;
            display: flex;
            align-items: center;
            justify-content: center;
            border-radius: 50%;
            transition: all 0.3s ease;
        }

        .modal-close:hover {
            background: rgba(148, 163, 184, 0.2);
            color: #f1f5f9;
        }

        .modal-body {
            padding: 30px;
        }

        .connection-status {
            padding: 10px 15px;
            border-radius: 8px;
            margin-top: 15px;
            display: none;
        }

        .connection-status.success {
            background: rgba(34, 197, 94, 0.1);
            border: 1px solid rgba(34, 197, 94, 0.3);
            color: #86efac;
            display: block;
        }

        .connection-status.error {
            background: rgba(239, 68, 68, 0.1);
            border: 1px solid rgba(239, 68, 68, 0.3);
            color: #fca5a5;
            display: block;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1><i class="fas fa-brain"></i> Document Q&A System</h1>
            <p>Upload documents or provide URLs, ask questions, and get intelligent answers</p>
            <div class="config-panel">
                <button id="configBtn" class="btn btn-secondary" onclick="toggleConfig()">
                    <i class="fas fa-cog"></i> Configuration
                </button>
            </div>
        </div>

        <!-- Configuration Modal -->
        <div id="configModal" class="modal">
            <div class="modal-content">
                <div class="modal-header">
                    <h3><i class="fas fa-cog"></i> API Configuration</h3>
                    <button class="modal-close" onclick="toggleConfig()">&times;</button>
                </div>
                <div class="modal-body">
                    <div class="input-group">
                        <label class="input-label">API Base URL</label>
                        <input type="url" id="apiUrlInput" class="input-field" value="http://localhost:8000">
                    </div>
                    <div class="input-group">
                        <label class="input-label">Bearer Token</label>
                        <input type="password" id="tokenInput" class="input-field" placeholder="Enter your API bearer token">
                    </div>
                    <div class="input-group">
                        <button class="btn" onclick="saveConfig()">
                            <i class="fas fa-save"></i> Save Configuration
                        </button>
                        <button class="btn btn-secondary" onclick="testConnection()">
                            <i class="fas fa-plug"></i> Test Connection
                        </button>
                    </div>
                </div>
            </div>
        </div>

        <div class="main-content">
            <!-- Document Input Section -->
            <div class="card">
                <h2 class="card-title">
                    <i class="fas fa-file-upload"></i>
                    Document Input
                </h2>
                
                <div class="tab-buttons">
                    <div class="tab-button active" onclick="switchTab('upload')">
                        <i class="fas fa-upload"></i> Upload File
                    </div>
                    <div class="tab-button" onclick="switchTab('url')">
                        <i class="fas fa-link"></i> URL Input
                    </div>
                </div>

                <div id="upload-tab" class="tab-content active">
                    <div class="input-group">
                        <label class="input-label">Upload Document (PDF, DOCX)</label>
                        <div class="file-upload">
                            <input type="file" id="fileInput" accept=".pdf,.docx,.doc">
                            <label for="fileInput" class="file-upload-label">
                                <i class="fas fa-cloud-upload-alt"></i>
                                <span>Click to upload or drag and drop</span>
                            </label>
                        </div>
                    </div>
                </div>

                <div id="url-tab" class="tab-content">
                    <div class="input-group">
                        <label class="input-label">Document URL</label>
                        <input type="url" id="urlInput" class="input-field" 
                               placeholder="https://example.com/document.pdf">
                    </div>
                </div>

                <div class="input-group">
                    <button id="processBtn" class="btn" onclick="processDocument()">
                        <i class="fas fa-cogs"></i>
                        Process Document
                    </button>
                </div>

                <!-- Document Statistics -->
                <div id="docStats" style="display: none;">
                    <h3 style="margin-bottom: 15px; color: #f1f5f9;">
                        <i class="fas fa-chart-bar"></i> Document Statistics
                    </h3>
                    <div class="stats-grid">
                        <div class="stat-item">
                            <span id="charCount" class="stat-value">0</span>
                            <span class="stat-label">Characters</span>
                        </div>
                        <div class="stat-item">
                            <span id="wordCount" class="stat-value">0</span>
                            <span class="stat-label">Words</span>
                        </div>
                        <div class="stat-item">
                            <span id="lineCount" class="stat-value">0</span>
                            <span class="stat-label">Lines</span>
                        </div>
                        <div class="stat-item">
                            <span id="processTime" class="stat-value">0ms</span>
                            <span class="stat-label">Process Time</span>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Questions Section -->
            <div class="card">
                <h2 class="card-title">
                    <i class="fas fa-question-circle"></i>
                    Questions
                </h2>
                
                <div class="input-group">
                    <label class="input-label">Enter your questions (one per line)</label>
                    <textarea id="questionsInput" class="input-field textarea" 
                              placeholder="What is the main topic of this document?&#10;What are the key points mentioned?&#10;Can you summarize the conclusion?"></textarea>
                </div>

                <div class="input-group">
                    <button id="askBtn" class="btn" onclick="askQuestions()" disabled>
                        <i class="fas fa-paper-plane"></i>
                        Ask Questions
                    </button>
                    <button class="btn btn-secondary" onclick="clearResults()">
                        <i class="fas fa-trash"></i>
                        Clear Results
                    </button>
                </div>
            </div>

            <!-- Results Section -->
            <div class="card results-section">
                <h2 class="card-title">
                    <i class="fas fa-comments"></i>
                    Questions & Answers
                </h2>

                <div id="loading" class="loading">
                    <div class="spinner"></div>
                    <p>Processing your questions...</p>
                </div>

                <div id="error" class="error"></div>
                <div id="success" class="success"></div>

                <div id="resultsContainer">
                    <table id="resultsTable" class="results-table" style="display: none;">
                        <thead>
                            <tr>
                                <th style="width: 40%;">Question</th>
                                <th style="width: 50%;">Answer</th>
                                <th style="width: 10%;">Time (ms)</th>
                            </tr>
                        </thead>
                        <tbody id="resultsBody">
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
    </div>

    <script src="app.js"></script>
</body>
</html>
