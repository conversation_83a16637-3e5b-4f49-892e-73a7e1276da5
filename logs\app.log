2025-08-05 21:49:31 | INFO     | main:lifespan:33 - Starting LLM-Powered Query-Retrieval System
2025-08-05 21:49:31 | INFO     | services.vector_store:initialize:29 - Initializing vector store...
2025-08-05 21:49:31 | INFO     | services.embedding_service:initialize:33 - Initializing embedding service with provider: gemini
2025-08-05 21:49:31 | INFO     | services.embedding_service:_initialize_gemini:61 - Using Gemini model: models/embedding-001
2025-08-05 21:49:31 | INFO     | services.embedding_service:initialize:47 - Embedding service initialized with dimension: 768
2025-08-05 21:49:31 | INFO     | services.vector_store:_load_index:198 - No existing vector store found, starting fresh
2025-08-05 21:49:31 | INFO     | services.vector_store:initialize:42 - Vector store initialized with {'provider': 'gemini', 'dimension': 768, 'model': 'models/embedding-001'}
2025-08-05 21:49:31 | INFO     | main:lifespan:41 - System initialized successfully
2025-08-05 21:58:46 | INFO     | main:run_query:103 - Processing request with 10 questions
2025-08-05 21:58:46 | INFO     | services.document_processor:process_document:37 - Processing document from URL: https://hackrx.blob.core.windows.net/assets/policy.pdf?sv=2023-01-03&st=2025-07-04T09%3A11%3A24Z&se=2027-07-05T09%3A11%3A00Z&sr=b&sp=r&sig=N4a9OU0w0QXO6AOIBiu4bpl7AXvEZogeT%2FjUHNO7HzQ%3D
2025-08-05 21:58:47 | INFO     | services.document_processor:process_document:60 - Successfully extracted 109014 characters from document
2025-08-05 21:58:47 | INFO     | services.document_processor:chunk_text:204 - Created 273 chunks from text
2025-08-05 21:58:47 | INFO     | services.vector_store:add_documents:60 - Adding 273 chunks to vector store
2025-08-05 22:01:43 | INFO     | services.vector_store:_save_index:177 - Vector store saved to disk
2025-08-05 22:01:43 | INFO     | services.vector_store:add_documents:83 - Successfully added 273 chunks to vector store
2025-08-05 22:01:43 | INFO     | services.query_processor:process_query:37 - Processing query: 'What is the grace period for premium payment under the National Parivar Mediclaim Plus Policy?...'
2025-08-05 22:01:43 | INFO     | services.vector_store:search:105 - Searching for query: 'the grace period for premium payment under the Nat...' with top_k=3
2025-08-05 22:01:43 | INFO     | services.vector_store:search:127 - Found 3 search results
2025-08-05 22:01:43 | INFO     | services.vector_store:get_relevant_context:157 - Generated context with 1102 characters from 3 chunks
2025-08-05 22:01:43 | ERROR    | services.query_processor:_generate_answer:190 - Error generating answer with Gemini: __init__() got an unexpected keyword argument 'system_instruction'
2025-08-05 22:01:43 | INFO     | services.query_processor:process_query:55 - Generated answer with 88 characters
2025-08-05 22:01:43 | INFO     | services.query_processor:process_query:37 - Processing query: 'What is the waiting period for pre-existing diseases (PED) to be covered?...'
2025-08-05 22:01:43 | INFO     | services.vector_store:search:105 - Searching for query: 'the waiting period for pre-existing diseases (PED)...' with top_k=3
2025-08-05 22:01:44 | INFO     | services.vector_store:search:127 - Found 3 search results
2025-08-05 22:01:44 | INFO     | services.vector_store:get_relevant_context:157 - Generated context with 1359 characters from 3 chunks
2025-08-05 22:01:44 | ERROR    | services.query_processor:_generate_answer:190 - Error generating answer with Gemini: __init__() got an unexpected keyword argument 'system_instruction'
2025-08-05 22:01:44 | INFO     | services.query_processor:process_query:55 - Generated answer with 88 characters
2025-08-05 22:01:44 | INFO     | services.query_processor:process_query:37 - Processing query: 'Does this policy cover maternity expenses, and what are the conditions?...'
2025-08-05 22:01:44 | INFO     | services.vector_store:search:105 - Searching for query: 'this policy cover maternity expenses, and the cond...' with top_k=3
2025-08-05 22:01:44 | INFO     | services.vector_store:search:127 - Found 3 search results
2025-08-05 22:01:44 | INFO     | services.vector_store:get_relevant_context:157 - Generated context with 1285 characters from 3 chunks
2025-08-05 22:01:44 | ERROR    | services.query_processor:_generate_answer:190 - Error generating answer with Gemini: __init__() got an unexpected keyword argument 'system_instruction'
2025-08-05 22:01:44 | INFO     | services.query_processor:process_query:55 - Generated answer with 88 characters
2025-08-05 22:01:44 | INFO     | services.query_processor:process_query:37 - Processing query: 'What is the waiting period for cataract surgery?...'
2025-08-05 22:01:44 | INFO     | services.vector_store:search:105 - Searching for query: 'the waiting period for cataract surgery? waiting p...' with top_k=3
2025-08-05 22:01:45 | INFO     | services.vector_store:search:127 - Found 3 search results
2025-08-05 22:01:45 | INFO     | services.vector_store:get_relevant_context:157 - Generated context with 1341 characters from 3 chunks
2025-08-05 22:01:45 | ERROR    | services.query_processor:_generate_answer:190 - Error generating answer with Gemini: __init__() got an unexpected keyword argument 'system_instruction'
2025-08-05 22:01:45 | INFO     | services.query_processor:process_query:55 - Generated answer with 88 characters
2025-08-05 22:01:45 | INFO     | services.query_processor:process_query:37 - Processing query: 'Are the medical expenses for an organ donor covered under this policy?...'
2025-08-05 22:01:45 | INFO     | services.vector_store:search:105 - Searching for query: 'the medical expenses for an organ donor covered un...' with top_k=3
2025-08-05 22:01:45 | INFO     | services.vector_store:search:127 - Found 3 search results
2025-08-05 22:01:45 | INFO     | services.vector_store:get_relevant_context:157 - Generated context with 1382 characters from 3 chunks
2025-08-05 22:01:45 | ERROR    | services.query_processor:_generate_answer:190 - Error generating answer with Gemini: __init__() got an unexpected keyword argument 'system_instruction'
2025-08-05 22:01:45 | INFO     | services.query_processor:process_query:55 - Generated answer with 88 characters
2025-08-05 22:01:45 | INFO     | services.query_processor:process_query:37 - Processing query: 'What is the No Claim Discount (NCD) offered in this policy?...'
2025-08-05 22:01:45 | INFO     | services.vector_store:search:105 - Searching for query: 'the No Claim Discount (NCD) offered in this policy...' with top_k=3
2025-08-05 22:01:46 | INFO     | services.vector_store:search:127 - Found 3 search results
2025-08-05 22:01:46 | INFO     | services.vector_store:get_relevant_context:157 - Generated context with 1378 characters from 3 chunks
2025-08-05 22:01:46 | ERROR    | services.query_processor:_generate_answer:190 - Error generating answer with Gemini: __init__() got an unexpected keyword argument 'system_instruction'
2025-08-05 22:01:46 | INFO     | services.query_processor:process_query:55 - Generated answer with 88 characters
2025-08-05 22:01:46 | INFO     | services.query_processor:process_query:37 - Processing query: 'Is there a benefit for preventive health check-ups?...'
2025-08-05 22:01:46 | INFO     | services.vector_store:search:105 - Searching for query: 'there a benefit for preventive health check-ups? b...' with top_k=3
2025-08-05 22:01:46 | INFO     | services.vector_store:search:127 - Found 3 search results
2025-08-05 22:01:46 | INFO     | services.vector_store:get_relevant_context:157 - Generated context with 1294 characters from 3 chunks
2025-08-05 22:01:46 | ERROR    | services.query_processor:_generate_answer:190 - Error generating answer with Gemini: __init__() got an unexpected keyword argument 'system_instruction'
2025-08-05 22:01:46 | INFO     | services.query_processor:process_query:55 - Generated answer with 88 characters
2025-08-05 22:01:46 | INFO     | services.query_processor:process_query:37 - Processing query: 'How does the policy define a 'Hospital'?...'
2025-08-05 22:01:46 | INFO     | services.vector_store:search:105 - Searching for query: 'the policy define a 'Hospital'? policy hospital...' with top_k=3
2025-08-05 22:01:47 | INFO     | services.vector_store:search:127 - Found 3 search results
2025-08-05 22:01:47 | INFO     | services.vector_store:get_relevant_context:157 - Generated context with 1534 characters from 3 chunks
2025-08-05 22:01:47 | ERROR    | services.query_processor:_generate_answer:190 - Error generating answer with Gemini: __init__() got an unexpected keyword argument 'system_instruction'
2025-08-05 22:01:47 | INFO     | services.query_processor:process_query:55 - Generated answer with 88 characters
2025-08-05 22:01:47 | INFO     | services.query_processor:process_query:37 - Processing query: 'What is the extent of coverage for AYUSH treatments?...'
2025-08-05 22:01:47 | INFO     | services.vector_store:search:105 - Searching for query: 'the extent of coverage for AYUSH treatments? cover...' with top_k=3
2025-08-05 22:01:47 | INFO     | services.vector_store:search:127 - Found 3 search results
2025-08-05 22:01:47 | INFO     | services.vector_store:get_relevant_context:157 - Generated context with 1348 characters from 3 chunks
2025-08-05 22:01:47 | ERROR    | services.query_processor:_generate_answer:190 - Error generating answer with Gemini: __init__() got an unexpected keyword argument 'system_instruction'
2025-08-05 22:01:47 | INFO     | services.query_processor:process_query:55 - Generated answer with 88 characters
2025-08-05 22:01:47 | INFO     | services.query_processor:process_query:37 - Processing query: 'Are there any sub-limits on room rent and ICU charges for Plan A?...'
2025-08-05 22:01:47 | INFO     | services.vector_store:search:105 - Searching for query: 'there any sub-limits on room rent and ICU charges ...' with top_k=3
2025-08-05 22:01:48 | INFO     | services.vector_store:search:127 - Found 3 search results
2025-08-05 22:01:48 | INFO     | services.vector_store:get_relevant_context:157 - Generated context with 2309 characters from 3 chunks
2025-08-05 22:01:48 | ERROR    | services.query_processor:_generate_answer:190 - Error generating answer with Gemini: __init__() got an unexpected keyword argument 'system_instruction'
2025-08-05 22:01:48 | INFO     | services.query_processor:process_query:55 - Generated answer with 88 characters
2025-08-05 22:01:48 | INFO     | main:run_query:118 - Successfully processed 10 questions
2025-08-05 22:12:18 | INFO     | main:run_query:103 - Processing request with 6 questions
2025-08-05 22:12:18 | INFO     | services.document_processor:process_document:37 - Processing document from URL: https://hackrx.blob.core.windows.net/assets/policy.pdf?sv=2023-01-03&st=2025-07-04T09%3A11%3A24Z&se=2027-07-05T09%3A11%3A00Z&sr=b&sp=r&sig=N4a9OU0w0QXO6AOIBiu4bpl7AXvEZogeT%2FjUHNO7HzQ%3D
2025-08-05 22:12:19 | INFO     | services.document_processor:process_document:60 - Successfully extracted 109014 characters from document
2025-08-05 22:12:19 | INFO     | services.document_processor:chunk_text:204 - Created 273 chunks from text
2025-08-05 22:12:19 | INFO     | services.vector_store:add_documents:60 - Adding 273 chunks to vector store
2025-08-05 22:15:19 | INFO     | services.vector_store:_save_index:177 - Vector store saved to disk
2025-08-05 22:15:19 | INFO     | services.vector_store:add_documents:83 - Successfully added 273 chunks to vector store
2025-08-05 22:15:19 | INFO     | services.query_processor:process_query:37 - Processing query: 'What is the grace period for premium payment?...'
2025-08-05 22:15:19 | INFO     | services.vector_store:search:105 - Searching for query: 'the grace period for premium payment? premium grac...' with top_k=3
2025-08-05 22:15:19 | INFO     | services.vector_store:search:127 - Found 3 search results
2025-08-05 22:15:19 | INFO     | services.vector_store:get_relevant_context:157 - Generated context with 1128 characters from 3 chunks
2025-08-05 22:15:19 | ERROR    | services.query_processor:_generate_answer:190 - Error generating answer with Gemini: __init__() got an unexpected keyword argument 'system_instruction'
2025-08-05 22:15:19 | INFO     | services.query_processor:process_query:55 - Generated answer with 88 characters
2025-08-05 22:15:19 | INFO     | services.query_processor:process_query:37 - Processing query: 'What is the waiting period for pre-existing diseases?...'
2025-08-05 22:15:19 | INFO     | services.vector_store:search:105 - Searching for query: 'the waiting period for pre-existing diseases? wait...' with top_k=3
2025-08-05 22:15:20 | INFO     | services.vector_store:search:127 - Found 3 search results
2025-08-05 22:15:20 | INFO     | services.vector_store:get_relevant_context:157 - Generated context with 1380 characters from 3 chunks
2025-08-05 22:15:20 | ERROR    | services.query_processor:_generate_answer:190 - Error generating answer with Gemini: __init__() got an unexpected keyword argument 'system_instruction'
2025-08-05 22:15:20 | INFO     | services.query_processor:process_query:55 - Generated answer with 88 characters
2025-08-05 22:15:20 | INFO     | services.query_processor:process_query:37 - Processing query: 'Does this policy cover maternity expenses?...'
2025-08-05 22:15:20 | INFO     | services.vector_store:search:105 - Searching for query: 'this policy cover maternity expenses? policy mater...' with top_k=3
2025-08-05 22:15:20 | INFO     | services.vector_store:search:127 - Found 3 search results
2025-08-05 22:15:20 | INFO     | services.vector_store:get_relevant_context:157 - Generated context with 1285 characters from 3 chunks
2025-08-05 22:15:20 | ERROR    | services.query_processor:_generate_answer:190 - Error generating answer with Gemini: __init__() got an unexpected keyword argument 'system_instruction'
2025-08-05 22:15:20 | INFO     | services.query_processor:process_query:55 - Generated answer with 88 characters
2025-08-05 22:15:20 | INFO     | services.query_processor:process_query:37 - Processing query: 'Is there a benefit for preventive health check-ups?...'
2025-08-05 22:15:20 | INFO     | services.vector_store:search:105 - Searching for query: 'there a benefit for preventive health check-ups? b...' with top_k=3
2025-08-05 22:15:21 | INFO     | services.vector_store:search:127 - Found 3 search results
2025-08-05 22:15:21 | INFO     | services.vector_store:get_relevant_context:157 - Generated context with 1294 characters from 3 chunks
2025-08-05 22:15:21 | ERROR    | services.query_processor:_generate_answer:190 - Error generating answer with Gemini: __init__() got an unexpected keyword argument 'system_instruction'
2025-08-05 22:15:21 | INFO     | services.query_processor:process_query:55 - Generated answer with 88 characters
2025-08-05 22:15:21 | INFO     | services.query_processor:process_query:37 - Processing query: 'How does the policy define a 'Hospital'?...'
2025-08-05 22:15:21 | INFO     | services.vector_store:search:105 - Searching for query: 'the policy define a 'Hospital'? policy hospital...' with top_k=3
2025-08-05 22:15:21 | INFO     | services.vector_store:search:127 - Found 3 search results
2025-08-05 22:15:21 | INFO     | services.vector_store:get_relevant_context:157 - Generated context with 1534 characters from 3 chunks
2025-08-05 22:15:21 | ERROR    | services.query_processor:_generate_answer:190 - Error generating answer with Gemini: __init__() got an unexpected keyword argument 'system_instruction'
2025-08-05 22:15:21 | INFO     | services.query_processor:process_query:55 - Generated answer with 88 characters
2025-08-05 22:15:21 | INFO     | services.query_processor:process_query:37 - Processing query: 'What is the extent of coverage for AYUSH treatments?...'
2025-08-05 22:15:21 | INFO     | services.vector_store:search:105 - Searching for query: 'the extent of coverage for AYUSH treatments? cover...' with top_k=3
2025-08-05 22:15:22 | INFO     | services.vector_store:search:127 - Found 3 search results
2025-08-05 22:15:22 | INFO     | services.vector_store:get_relevant_context:157 - Generated context with 1348 characters from 3 chunks
2025-08-05 22:15:22 | ERROR    | services.query_processor:_generate_answer:190 - Error generating answer with Gemini: __init__() got an unexpected keyword argument 'system_instruction'
2025-08-05 22:15:22 | INFO     | services.query_processor:process_query:55 - Generated answer with 88 characters
2025-08-05 22:15:22 | INFO     | main:run_query:118 - Successfully processed 6 questions
2025-08-05 22:17:40 | INFO     | main:lifespan:46 - Shutting down system
2025-08-05 22:17:48 | INFO     | main:lifespan:33 - Starting LLM-Powered Query-Retrieval System
2025-08-05 22:17:48 | INFO     | services.vector_store:initialize:29 - Initializing vector store...
2025-08-05 22:17:48 | INFO     | services.embedding_service:initialize:33 - Initializing embedding service with provider: gemini
2025-08-05 22:17:49 | INFO     | services.embedding_service:_initialize_gemini:61 - Using Gemini model: models/embedding-001
2025-08-05 22:17:49 | INFO     | services.embedding_service:initialize:47 - Embedding service initialized with dimension: 768
2025-08-05 22:17:49 | INFO     | services.vector_store:_load_index:196 - Loaded vector store with 273 chunks
2025-08-05 22:17:49 | INFO     | services.vector_store:initialize:42 - Vector store initialized with {'provider': 'gemini', 'dimension': 768, 'model': 'models/embedding-001'}
2025-08-05 22:17:49 | INFO     | main:lifespan:41 - System initialized successfully
2025-08-05 22:18:18 | INFO     | main:lifespan:46 - Shutting down system
2025-08-05 22:18:24 | INFO     | main:lifespan:33 - Starting LLM-Powered Query-Retrieval System
2025-08-05 22:18:24 | INFO     | services.vector_store:initialize:29 - Initializing vector store...
2025-08-05 22:18:24 | INFO     | services.embedding_service:initialize:33 - Initializing embedding service with provider: gemini
2025-08-05 22:18:25 | INFO     | services.embedding_service:_initialize_gemini:61 - Using Gemini model: models/embedding-001
2025-08-05 22:18:25 | INFO     | services.embedding_service:initialize:47 - Embedding service initialized with dimension: 768
2025-08-05 22:18:25 | INFO     | services.vector_store:_load_index:196 - Loaded vector store with 273 chunks
2025-08-05 22:18:25 | INFO     | services.vector_store:initialize:42 - Vector store initialized with {'provider': 'gemini', 'dimension': 768, 'model': 'models/embedding-001'}
2025-08-05 22:18:25 | INFO     | main:lifespan:41 - System initialized successfully
2025-08-05 22:20:00 | INFO     | main:run_query:103 - Processing request with 6 questions
2025-08-05 22:20:00 | INFO     | services.document_processor:process_document:37 - Processing document from URL: https://hackrx.blob.core.windows.net/assets/policy.pdf?sv=2023-01-03&st=2025-07-04T09%3A11%3A24Z&se=2027-07-05T09%3A11%3A00Z&sr=b&sp=r&sig=N4a9OU0w0QXO6AOIBiu4bpl7AXvEZogeT%2FjUHNO7HzQ%3D
2025-08-05 22:20:00 | INFO     | services.document_processor:process_document:60 - Successfully extracted 109014 characters from document
2025-08-05 22:20:00 | INFO     | services.document_processor:chunk_text:204 - Created 273 chunks from text
2025-08-05 22:20:00 | INFO     | services.vector_store:add_documents:60 - Adding 273 chunks to vector store
2025-08-05 22:22:46 | INFO     | services.vector_store:_save_index:177 - Vector store saved to disk
2025-08-05 22:22:46 | INFO     | services.vector_store:add_documents:83 - Successfully added 273 chunks to vector store
2025-08-05 22:22:46 | INFO     | services.query_processor:process_query:37 - Processing query: 'What is the grace period for premium payment?...'
2025-08-05 22:22:46 | INFO     | services.vector_store:search:105 - Searching for query: 'the grace period for premium payment? premium grac...' with top_k=3
2025-08-05 22:22:47 | INFO     | services.vector_store:search:127 - Found 3 search results
2025-08-05 22:22:47 | INFO     | services.vector_store:get_relevant_context:157 - Generated context with 1128 characters from 3 chunks
2025-08-05 22:22:48 | INFO     | services.query_processor:process_query:55 - Generated answer with 174 characters
2025-08-05 22:22:48 | INFO     | services.query_processor:process_query:37 - Processing query: 'What is the waiting period for pre-existing diseases?...'
2025-08-05 22:22:48 | INFO     | services.vector_store:search:105 - Searching for query: 'the waiting period for pre-existing diseases? wait...' with top_k=3
2025-08-05 22:22:48 | INFO     | services.vector_store:search:127 - Found 3 search results
2025-08-05 22:22:48 | INFO     | services.vector_store:get_relevant_context:157 - Generated context with 1380 characters from 3 chunks
2025-08-05 22:22:50 | INFO     | services.query_processor:process_query:55 - Generated answer with 361 characters
2025-08-05 22:22:50 | INFO     | services.query_processor:process_query:37 - Processing query: 'Does this policy cover maternity expenses?...'
2025-08-05 22:22:50 | INFO     | services.vector_store:search:105 - Searching for query: 'this policy cover maternity expenses? policy mater...' with top_k=3
2025-08-05 22:22:50 | INFO     | services.vector_store:search:127 - Found 3 search results
2025-08-05 22:22:50 | INFO     | services.vector_store:get_relevant_context:157 - Generated context with 1285 characters from 3 chunks
2025-08-05 22:22:55 | INFO     | services.query_processor:process_query:55 - Generated answer with 654 characters
2025-08-05 22:22:55 | INFO     | services.query_processor:process_query:37 - Processing query: 'Is there a benefit for preventive health check-ups?...'
2025-08-05 22:22:55 | INFO     | services.vector_store:search:105 - Searching for query: 'there a benefit for preventive health check-ups? b...' with top_k=3
2025-08-05 22:22:55 | INFO     | services.vector_store:search:127 - Found 3 search results
2025-08-05 22:22:55 | INFO     | services.vector_store:get_relevant_context:157 - Generated context with 1294 characters from 3 chunks
2025-08-05 22:22:56 | INFO     | services.query_processor:process_query:55 - Generated answer with 260 characters
2025-08-05 22:22:56 | INFO     | services.query_processor:process_query:37 - Processing query: 'How does the policy define a 'Hospital'?...'
2025-08-05 22:22:56 | INFO     | services.vector_store:search:105 - Searching for query: 'the policy define a 'Hospital'? policy hospital...' with top_k=3
2025-08-05 22:22:57 | INFO     | services.vector_store:search:127 - Found 3 search results
2025-08-05 22:22:57 | INFO     | services.vector_store:get_relevant_context:157 - Generated context with 1534 characters from 3 chunks
2025-08-05 22:23:02 | INFO     | services.query_processor:process_query:55 - Generated answer with 402 characters
2025-08-05 22:23:02 | INFO     | services.query_processor:process_query:37 - Processing query: 'What is the extent of coverage for AYUSH treatments?...'
2025-08-05 22:23:02 | INFO     | services.vector_store:search:105 - Searching for query: 'the extent of coverage for AYUSH treatments? cover...' with top_k=3
2025-08-05 22:23:03 | INFO     | services.vector_store:search:127 - Found 3 search results
2025-08-05 22:23:03 | INFO     | services.vector_store:get_relevant_context:157 - Generated context with 1348 characters from 3 chunks
2025-08-05 22:23:04 | INFO     | services.query_processor:process_query:55 - Generated answer with 372 characters
2025-08-05 22:23:04 | INFO     | main:run_query:118 - Successfully processed 6 questions
2025-08-05 22:30:17 | INFO     | main:run_query:103 - Processing request with 10 questions
2025-08-05 22:30:17 | INFO     | services.document_processor:process_document:37 - Processing document from URL: https://hackrx.blob.core.windows.net/assets/policy.pdf?sv=2023-01-03&st=2025-07-04T09%3A11%3A24Z&se=2027-07-05T09%3A11%3A00Z&sr=b&sp=r&sig=N4a9OU0w0QXO6AOIBiu4bpl7AXvEZogeT%2FjUHNO7HzQ%3D
2025-08-05 22:30:18 | INFO     | services.document_processor:process_document:60 - Successfully extracted 109014 characters from document
2025-08-05 22:30:18 | INFO     | services.document_processor:chunk_text:204 - Created 273 chunks from text
2025-08-05 22:30:18 | INFO     | services.vector_store:add_documents:60 - Adding 273 chunks to vector store
2025-08-05 22:33:14 | INFO     | services.vector_store:_save_index:177 - Vector store saved to disk
2025-08-05 22:33:14 | INFO     | services.vector_store:add_documents:83 - Successfully added 273 chunks to vector store
2025-08-05 22:33:14 | INFO     | services.query_processor:process_query:37 - Processing query: 'What is the grace period for premium payment under the National Parivar Mediclaim Plus Policy?...'
2025-08-05 22:33:14 | INFO     | services.vector_store:search:105 - Searching for query: 'the grace period for premium payment under the Nat...' with top_k=3
2025-08-05 22:33:14 | INFO     | services.vector_store:search:127 - Found 3 search results
2025-08-05 22:33:14 | INFO     | services.vector_store:get_relevant_context:157 - Generated context with 1102 characters from 3 chunks
2025-08-05 22:33:15 | INFO     | services.query_processor:process_query:55 - Generated answer with 207 characters
2025-08-05 22:33:15 | INFO     | services.query_processor:process_query:37 - Processing query: 'What is the waiting period for pre-existing diseases (PED) to be covered?...'
2025-08-05 22:33:15 | INFO     | services.vector_store:search:105 - Searching for query: 'the waiting period for pre-existing diseases (PED)...' with top_k=3
2025-08-05 22:33:16 | INFO     | services.vector_store:search:127 - Found 3 search results
2025-08-05 22:33:16 | INFO     | services.vector_store:get_relevant_context:157 - Generated context with 1359 characters from 3 chunks
2025-08-05 22:33:17 | INFO     | services.query_processor:process_query:55 - Generated answer with 318 characters
2025-08-05 22:33:17 | INFO     | services.query_processor:process_query:37 - Processing query: 'Does this policy cover maternity expenses, and what are the conditions?...'
2025-08-05 22:33:17 | INFO     | services.vector_store:search:105 - Searching for query: 'this policy cover maternity expenses, and the cond...' with top_k=3
2025-08-05 22:33:17 | INFO     | services.vector_store:search:127 - Found 3 search results
2025-08-05 22:33:17 | INFO     | services.vector_store:get_relevant_context:157 - Generated context with 1285 characters from 3 chunks
2025-08-05 22:33:19 | INFO     | services.query_processor:process_query:55 - Generated answer with 828 characters
2025-08-05 22:33:19 | INFO     | services.query_processor:process_query:37 - Processing query: 'What is the waiting period for cataract surgery?...'
2025-08-05 22:33:19 | INFO     | services.vector_store:search:105 - Searching for query: 'the waiting period for cataract surgery? waiting p...' with top_k=3
2025-08-05 22:33:20 | INFO     | services.vector_store:search:127 - Found 3 search results
2025-08-05 22:33:20 | INFO     | services.vector_store:get_relevant_context:157 - Generated context with 1341 characters from 3 chunks
2025-08-05 22:33:22 | INFO     | services.query_processor:process_query:55 - Generated answer with 147 characters
2025-08-05 22:33:22 | INFO     | services.query_processor:process_query:37 - Processing query: 'Are the medical expenses for an organ donor covered under this policy?...'
2025-08-05 22:33:22 | INFO     | services.vector_store:search:105 - Searching for query: 'the medical expenses for an organ donor covered un...' with top_k=3
2025-08-05 22:33:22 | INFO     | services.vector_store:search:127 - Found 3 search results
2025-08-05 22:33:22 | INFO     | services.vector_store:get_relevant_context:157 - Generated context with 1382 characters from 3 chunks
2025-08-05 22:33:23 | INFO     | services.query_processor:process_query:55 - Generated answer with 635 characters
2025-08-05 22:33:23 | INFO     | services.query_processor:process_query:37 - Processing query: 'What is the No Claim Discount (NCD) offered in this policy?...'
2025-08-05 22:33:23 | INFO     | services.vector_store:search:105 - Searching for query: 'the No Claim Discount (NCD) offered in this policy...' with top_k=3
2025-08-05 22:33:24 | INFO     | services.vector_store:search:127 - Found 3 search results
2025-08-05 22:33:24 | INFO     | services.vector_store:get_relevant_context:157 - Generated context with 1378 characters from 3 chunks
2025-08-05 22:33:25 | INFO     | services.query_processor:process_query:55 - Generated answer with 262 characters
2025-08-05 22:33:25 | INFO     | services.query_processor:process_query:37 - Processing query: 'Is there a benefit for preventive health check-ups?...'
2025-08-05 22:33:25 | INFO     | services.vector_store:search:105 - Searching for query: 'there a benefit for preventive health check-ups? b...' with top_k=3
2025-08-05 22:33:26 | INFO     | services.vector_store:search:127 - Found 3 search results
2025-08-05 22:33:26 | INFO     | services.vector_store:get_relevant_context:157 - Generated context with 1294 characters from 3 chunks
2025-08-05 22:33:27 | INFO     | services.query_processor:process_query:55 - Generated answer with 260 characters
2025-08-05 22:33:27 | INFO     | services.query_processor:process_query:37 - Processing query: 'How does the policy define a 'Hospital'?...'
2025-08-05 22:33:27 | INFO     | services.vector_store:search:105 - Searching for query: 'the policy define a 'Hospital'? policy hospital...' with top_k=3
2025-08-05 22:33:27 | INFO     | services.vector_store:search:127 - Found 3 search results
2025-08-05 22:33:27 | INFO     | services.vector_store:get_relevant_context:157 - Generated context with 1534 characters from 3 chunks
2025-08-05 22:33:30 | INFO     | services.query_processor:process_query:55 - Generated answer with 402 characters
2025-08-05 22:33:30 | INFO     | services.query_processor:process_query:37 - Processing query: 'What is the extent of coverage for AYUSH treatments?...'
2025-08-05 22:33:30 | INFO     | services.vector_store:search:105 - Searching for query: 'the extent of coverage for AYUSH treatments? cover...' with top_k=3
2025-08-05 22:33:31 | INFO     | services.vector_store:search:127 - Found 3 search results
2025-08-05 22:33:31 | INFO     | services.vector_store:get_relevant_context:157 - Generated context with 1348 characters from 3 chunks
2025-08-05 22:33:32 | INFO     | services.query_processor:process_query:55 - Generated answer with 372 characters
2025-08-05 22:33:32 | INFO     | services.query_processor:process_query:37 - Processing query: 'Are there any sub-limits on room rent and ICU charges for Plan A?...'
2025-08-05 22:33:32 | INFO     | services.vector_store:search:105 - Searching for query: 'there any sub-limits on room rent and ICU charges ...' with top_k=3
2025-08-05 22:33:33 | INFO     | services.vector_store:search:127 - Found 3 search results
2025-08-05 22:33:33 | INFO     | services.vector_store:get_relevant_context:157 - Generated context with 2309 characters from 3 chunks
2025-08-05 22:33:34 | INFO     | services.query_processor:process_query:55 - Generated answer with 237 characters
2025-08-05 22:33:34 | INFO     | main:run_query:118 - Successfully processed 10 questions
2025-08-05 22:52:09 | INFO     | main:lifespan:46 - Shutting down system
2025-08-07 08:21:33 | INFO     | main:lifespan:35 - Starting LLM-Powered Query-Retrieval System
2025-08-07 08:21:33 | INFO     | services.vector_store:initialize:30 - Initializing vector store...
2025-08-07 08:21:33 | INFO     | services.embedding_service:initialize:34 - Initializing embedding service with provider: gemini
2025-08-07 08:21:33 | INFO     | services.embedding_service:_initialize_gemini:54 - Using Gemini model: models/embedding-001
2025-08-07 08:21:34 | INFO     | services.embedding_service:initialize:39 - Embedding service initialized with dimension: 768
2025-08-07 08:21:34 | INFO     | services.embedding_service:initialize:40 - Using Gemini embeddings for optimal performance
2025-08-07 08:21:34 | INFO     | services.vector_store:_load_index:205 - Loaded vector store with 273 chunks
2025-08-07 08:21:34 | INFO     | services.vector_store:initialize:43 - Vector store initialized with {'provider': 'gemini', 'dimension': 768, 'model': 'models/embedding-001'}
2025-08-07 08:21:34 | INFO     | main:lifespan:43 - System initialized successfully
2025-08-07 08:25:05 | INFO     | main:run_query:105 - Processing request with 2 questions
2025-08-07 08:25:05 | INFO     | services.document_processor:process_document:38 - Processing document from URL: https://example.com/document.pdf
2025-08-07 08:25:35 | ERROR    | services.document_processor:process_document:69 - Error processing document: 404 Client Error: Not Found for url: https://example.com/document.pdf
2025-08-07 08:25:35 | ERROR    | main:run_query:131 - Error processing request: 404 Client Error: Not Found for url: https://example.com/document.pdf
2025-08-07 08:26:17 | INFO     | main:run_query:105 - Processing request with 3 questions
2025-08-07 08:26:17 | INFO     | services.document_processor:process_document:38 - Processing document from URL: https://hackrx.blob.core.windows.net/assets/policy.pdf?sv=2023-01-03&st=2025-07-04T09%3A11%3A24Z&se=2027-07-05T09%3A11%3A00Z&sr=b&sp=r&sig=N4a9OU0w0QXO6AOIBiu4bpl7AXvEZogeT%2FjUHNO7HzQ%3D
2025-08-07 08:26:19 | INFO     | services.document_processor:process_document:61 - Successfully extracted 109014 characters from document
2025-08-07 08:26:19 | INFO     | services.document_processor:chunk_text:205 - Created 124 chunks from text
2025-08-07 08:26:19 | INFO     | services.vector_store:add_documents:67 - Adding 124 chunks to vector store
2025-08-07 08:26:19 | INFO     | services.vector_store:add_documents:75 - Generating embeddings...
2025-08-07 08:26:34 | INFO     | services.vector_store:_save_index:186 - Vector store saved to disk
2025-08-07 08:26:34 | INFO     | services.vector_store:add_documents:92 - Successfully added 124 chunks to vector store
2025-08-07 08:26:34 | INFO     | main:run_query:122 - Processing queries concurrently...
2025-08-07 08:26:34 | INFO     | services.query_processor:process_query:37 - Processing query: 'What is the grace period for premium payment?...'
2025-08-07 08:26:34 | INFO     | services.vector_store:search:114 - Searching for query: 'the grace period for premium payment? premium grac...' with top_k=2
2025-08-07 08:26:34 | INFO     | services.vector_store:search:136 - Found 2 search results
2025-08-07 08:26:34 | INFO     | services.vector_store:get_relevant_context:166 - Generated context with 1923 characters from 2 chunks
2025-08-07 08:26:36 | INFO     | services.query_processor:process_query:55 - Generated answer with 164 characters
2025-08-07 08:26:36 | INFO     | services.query_processor:process_query:37 - Processing query: 'What is the waiting period for cataract surgery?...'
2025-08-07 08:26:36 | INFO     | services.vector_store:search:114 - Searching for query: 'the waiting period for cataract surgery? waiting p...' with top_k=2
2025-08-07 08:26:37 | INFO     | services.vector_store:search:136 - Found 2 search results
2025-08-07 08:26:37 | INFO     | services.vector_store:get_relevant_context:166 - Generated context with 2066 characters from 2 chunks
2025-08-07 08:26:38 | INFO     | services.query_processor:process_query:55 - Generated answer with 119 characters
2025-08-07 08:26:38 | INFO     | services.query_processor:process_query:37 - Processing query: 'What is the waiting period for pre-existing diseases?...'
2025-08-07 08:26:38 | INFO     | services.vector_store:search:114 - Searching for query: 'the waiting period for pre-existing diseases? wait...' with top_k=2
2025-08-07 08:26:39 | INFO     | services.vector_store:search:136 - Found 2 search results
2025-08-07 08:26:39 | INFO     | services.vector_store:get_relevant_context:166 - Generated context with 1952 characters from 2 chunks
2025-08-07 08:26:40 | INFO     | services.query_processor:process_query:55 - Generated answer with 332 characters
2025-08-07 08:26:40 | INFO     | main:run_query:127 - Successfully processed 3 questions
2025-08-07 08:29:27 | INFO     | main:lifespan:48 - Shutting down system
2025-08-07 08:30:58 | INFO     | main:lifespan:35 - Starting LLM-Powered Query-Retrieval System
2025-08-07 08:30:58 | INFO     | services.vector_store:initialize:30 - Initializing vector store...
2025-08-07 08:30:58 | INFO     | services.embedding_service:initialize:34 - Initializing embedding service with provider: gemini
2025-08-07 08:30:58 | INFO     | services.embedding_service:_initialize_gemini:54 - Using Gemini model: models/embedding-001
2025-08-07 08:30:58 | INFO     | services.embedding_service:initialize:39 - Embedding service initialized with dimension: 768
2025-08-07 08:30:58 | INFO     | services.embedding_service:initialize:40 - Using Gemini embeddings for optimal performance
2025-08-07 08:30:58 | INFO     | services.vector_store:_load_index:205 - Loaded vector store with 124 chunks
2025-08-07 08:30:58 | INFO     | services.vector_store:initialize:43 - Vector store initialized with {'provider': 'gemini', 'dimension': 768, 'model': 'models/embedding-001'}
2025-08-07 08:30:58 | INFO     | main:lifespan:43 - System initialized successfully
2025-08-07 08:31:32 | INFO     | main:run_query:105 - Processing request with 3 questions
2025-08-07 08:31:32 | INFO     | services.document_processor:process_document:38 - Processing document from URL: https://hackrx.blob.core.windows.net/assets/policy.pdf?sv=2023-01-03&st=2025-07-04T09%3A11%3A24Z&se=2027-07-05T09%3A11%3A00Z&sr=b&sp=r&sig=N4a9OU0w0QXO6AOIBiu4bpl7AXvEZogeT%2FjUHNO7HzQ%3D
2025-08-07 08:31:33 | INFO     | services.document_processor:process_document:61 - Successfully extracted 109014 characters from document
2025-08-07 08:31:33 | INFO     | services.document_processor:chunk_text:205 - Created 124 chunks from text
2025-08-07 08:31:33 | INFO     | services.vector_store:add_documents:67 - Adding 124 chunks to vector store
2025-08-07 08:31:33 | INFO     | services.vector_store:add_documents:75 - Generating embeddings...
2025-08-07 08:31:48 | INFO     | services.vector_store:_save_index:186 - Vector store saved to disk
2025-08-07 08:31:48 | INFO     | services.vector_store:add_documents:92 - Successfully added 124 chunks to vector store
2025-08-07 08:31:48 | INFO     | main:run_query:122 - Processing queries concurrently...
2025-08-07 08:31:48 | INFO     | services.query_processor:process_query:37 - Processing query: 'What is the grace period for premium payment?...'
2025-08-07 08:31:48 | INFO     | services.vector_store:search:114 - Searching for query: 'the grace period for premium payment? premium grac...' with top_k=2
2025-08-07 08:31:48 | INFO     | services.vector_store:search:136 - Found 2 search results
2025-08-07 08:31:48 | INFO     | services.vector_store:get_relevant_context:166 - Generated context with 1923 characters from 2 chunks
2025-08-07 08:31:50 | INFO     | services.query_processor:process_query:55 - Generated answer with 164 characters
2025-08-07 08:31:50 | INFO     | services.query_processor:process_query:37 - Processing query: 'What is the waiting period for cataract surgery?...'
2025-08-07 08:31:50 | INFO     | services.vector_store:search:114 - Searching for query: 'the waiting period for cataract surgery? waiting p...' with top_k=2
2025-08-07 08:31:50 | INFO     | services.vector_store:search:136 - Found 2 search results
2025-08-07 08:31:50 | INFO     | services.vector_store:get_relevant_context:166 - Generated context with 2066 characters from 2 chunks
2025-08-07 08:31:52 | INFO     | services.query_processor:process_query:55 - Generated answer with 119 characters
2025-08-07 08:31:52 | INFO     | services.query_processor:process_query:37 - Processing query: 'What is the waiting period for pre-existing diseases?...'
2025-08-07 08:31:52 | INFO     | services.vector_store:search:114 - Searching for query: 'the waiting period for pre-existing diseases? wait...' with top_k=2
2025-08-07 08:31:52 | INFO     | services.vector_store:search:136 - Found 2 search results
2025-08-07 08:31:52 | INFO     | services.vector_store:get_relevant_context:166 - Generated context with 1952 characters from 2 chunks
2025-08-07 08:31:54 | INFO     | services.query_processor:process_query:55 - Generated answer with 332 characters
2025-08-07 08:31:54 | INFO     | main:run_query:127 - Successfully processed 3 questions
2025-08-07 08:33:27 | INFO     | main:run_query:105 - Processing request with 10 questions
2025-08-07 08:33:27 | INFO     | services.document_processor:process_document:38 - Processing document from URL: https://hackrx.blob.core.windows.net/assets/policy.pdf?sv=2023-01-03&st=2025-07-04T09%3A11%3A24Z&se=2027-07-05T09%3A11%3A00Z&sr=b&sp=r&sig=N4a9OU0w0QXO6AOIBiu4bpl7AXvEZogeT%2FjUHNO7HzQ%3D
2025-08-07 08:33:28 | INFO     | services.document_processor:process_document:61 - Successfully extracted 109014 characters from document
2025-08-07 08:33:28 | INFO     | services.document_processor:chunk_text:205 - Created 124 chunks from text
2025-08-07 08:33:28 | INFO     | services.vector_store:add_documents:64 - Document already processed (hash: d913b509...), skipping embedding generation
2025-08-07 08:33:28 | INFO     | main:run_query:122 - Processing queries concurrently...
2025-08-07 08:33:28 | INFO     | services.query_processor:process_query:37 - Processing query: 'What is the grace period for premium payment under the National Parivar Mediclaim Plus Policy?...'
2025-08-07 08:33:28 | INFO     | services.vector_store:search:114 - Searching for query: 'the grace period for premium payment under the Nat...' with top_k=2
2025-08-07 08:33:28 | INFO     | services.vector_store:search:136 - Found 2 search results
2025-08-07 08:33:28 | INFO     | services.vector_store:get_relevant_context:166 - Generated context with 2191 characters from 2 chunks
2025-08-07 08:33:30 | INFO     | services.query_processor:process_query:55 - Generated answer with 154 characters
2025-08-07 08:33:30 | INFO     | services.query_processor:process_query:37 - Processing query: 'What is the waiting period for pre-existing diseases (PED) to be covered?...'
2025-08-07 08:33:30 | INFO     | services.vector_store:search:114 - Searching for query: 'the waiting period for pre-existing diseases (PED)...' with top_k=2
2025-08-07 08:33:31 | INFO     | services.vector_store:search:136 - Found 2 search results
2025-08-07 08:33:31 | INFO     | services.vector_store:get_relevant_context:166 - Generated context with 1914 characters from 2 chunks
2025-08-07 08:33:32 | INFO     | services.query_processor:process_query:55 - Generated answer with 379 characters
2025-08-07 08:33:32 | INFO     | services.query_processor:process_query:37 - Processing query: 'Does this policy cover maternity expenses, and what are the conditions?...'
2025-08-07 08:33:32 | INFO     | services.vector_store:search:114 - Searching for query: 'this policy cover maternity expenses, and the cond...' with top_k=2
2025-08-07 08:33:33 | INFO     | services.vector_store:search:136 - Found 2 search results
2025-08-07 08:33:33 | INFO     | services.vector_store:get_relevant_context:166 - Generated context with 1969 characters from 2 chunks
2025-08-07 08:33:35 | INFO     | services.query_processor:process_query:55 - Generated answer with 703 characters
2025-08-07 08:33:35 | INFO     | services.query_processor:process_query:37 - Processing query: 'What is the waiting period for cataract surgery?...'
2025-08-07 08:33:35 | INFO     | services.vector_store:search:114 - Searching for query: 'the waiting period for cataract surgery? waiting p...' with top_k=2
2025-08-07 08:33:36 | INFO     | services.vector_store:search:136 - Found 2 search results
2025-08-07 08:33:36 | INFO     | services.vector_store:get_relevant_context:166 - Generated context with 2066 characters from 2 chunks
2025-08-07 08:33:37 | INFO     | services.query_processor:process_query:55 - Generated answer with 119 characters
2025-08-07 08:33:37 | INFO     | services.query_processor:process_query:37 - Processing query: 'Are the medical expenses for an organ donor covered under this policy?...'
2025-08-07 08:33:37 | INFO     | services.vector_store:search:114 - Searching for query: 'the medical expenses for an organ donor covered un...' with top_k=2
2025-08-07 08:33:38 | INFO     | services.vector_store:search:136 - Found 2 search results
2025-08-07 08:33:38 | INFO     | services.vector_store:get_relevant_context:166 - Generated context with 1999 characters from 2 chunks
2025-08-07 08:33:40 | INFO     | services.query_processor:process_query:55 - Generated answer with 658 characters
2025-08-07 08:33:40 | INFO     | services.query_processor:process_query:37 - Processing query: 'What is the No Claim Discount (NCD) offered in this policy?...'
2025-08-07 08:33:40 | INFO     | services.vector_store:search:114 - Searching for query: 'the No Claim Discount (NCD) offered in this policy...' with top_k=2
2025-08-07 08:33:41 | INFO     | services.vector_store:search:136 - Found 2 search results
2025-08-07 08:33:41 | INFO     | services.vector_store:get_relevant_context:166 - Generated context with 1841 characters from 2 chunks
2025-08-07 08:33:42 | INFO     | services.query_processor:process_query:55 - Generated answer with 333 characters
2025-08-07 08:33:42 | INFO     | services.query_processor:process_query:37 - Processing query: 'Is there a benefit for preventive health check-ups?...'
2025-08-07 08:33:42 | INFO     | services.vector_store:search:114 - Searching for query: 'there a benefit for preventive health check-ups? b...' with top_k=2
2025-08-07 08:33:43 | INFO     | services.vector_store:search:136 - Found 2 search results
2025-08-07 08:33:43 | INFO     | services.vector_store:get_relevant_context:166 - Generated context with 3134 characters from 2 chunks
2025-08-07 08:33:45 | INFO     | services.query_processor:process_query:55 - Generated answer with 572 characters
2025-08-07 08:33:45 | INFO     | services.query_processor:process_query:37 - Processing query: 'How does the policy define a 'Hospital'?...'
2025-08-07 08:33:45 | INFO     | services.vector_store:search:114 - Searching for query: 'the policy define a 'Hospital'? policy hospital...' with top_k=2
2025-08-07 08:33:46 | INFO     | services.vector_store:search:136 - Found 2 search results
2025-08-07 08:33:46 | INFO     | services.vector_store:get_relevant_context:166 - Generated context with 1943 characters from 2 chunks
2025-08-07 08:33:48 | INFO     | services.query_processor:process_query:55 - Generated answer with 678 characters
2025-08-07 08:33:48 | INFO     | services.query_processor:process_query:37 - Processing query: 'What is the extent of coverage for AYUSH treatments?...'
2025-08-07 08:33:48 | INFO     | services.vector_store:search:114 - Searching for query: 'the extent of coverage for AYUSH treatments? cover...' with top_k=2
2025-08-07 08:33:48 | INFO     | services.vector_store:search:136 - Found 2 search results
2025-08-07 08:33:48 | INFO     | services.vector_store:get_relevant_context:166 - Generated context with 1762 characters from 2 chunks
2025-08-07 08:33:50 | INFO     | services.query_processor:process_query:55 - Generated answer with 274 characters
2025-08-07 08:33:50 | INFO     | services.query_processor:process_query:37 - Processing query: 'Are there any sub-limits on room rent and ICU charges for Plan A?...'
2025-08-07 08:33:50 | INFO     | services.vector_store:search:114 - Searching for query: 'there any sub-limits on room rent and ICU charges ...' with top_k=2
2025-08-07 08:33:51 | INFO     | services.vector_store:search:136 - Found 2 search results
2025-08-07 08:33:51 | INFO     | services.vector_store:get_relevant_context:166 - Generated context with 2472 characters from 2 chunks
2025-08-07 08:33:53 | INFO     | services.query_processor:process_query:55 - Generated answer with 281 characters
2025-08-07 08:33:53 | INFO     | main:run_query:127 - Successfully processed 10 questions
2025-08-07 08:41:51 | INFO     | main:run_query:105 - Processing request with 3 questions
2025-08-07 08:41:51 | INFO     | services.document_processor:process_document:38 - Processing document from URL: https://drive.google.com/uc?export=download&id=1l9C5DmyxNgNdXGkhVkJ53V491tat1H1x
2025-08-07 08:41:54 | INFO     | services.document_processor:process_document:61 - Successfully extracted 213274 characters from document
2025-08-07 08:41:54 | INFO     | services.document_processor:chunk_text:205 - Created 248 chunks from text
2025-08-07 08:41:54 | INFO     | services.vector_store:add_documents:67 - Adding 248 chunks to vector store
2025-08-07 08:41:54 | INFO     | services.vector_store:add_documents:75 - Generating embeddings...
2025-08-07 08:42:22 | INFO     | services.vector_store:_save_index:186 - Vector store saved to disk
2025-08-07 08:42:22 | INFO     | services.vector_store:add_documents:92 - Successfully added 248 chunks to vector store
2025-08-07 08:42:22 | INFO     | main:run_query:122 - Processing queries concurrently...
2025-08-07 08:42:22 | INFO     | services.query_processor:process_query:37 - Processing query: 'What is the main topic of this document?...'
2025-08-07 08:42:22 | INFO     | services.vector_store:search:114 - Searching for query: 'the main topic of this document?...' with top_k=2
2025-08-07 08:42:22 | INFO     | services.vector_store:search:136 - Found 2 search results
2025-08-07 08:42:22 | INFO     | services.vector_store:get_relevant_context:166 - Generated context with 1646 characters from 2 chunks
2025-08-07 08:42:25 | INFO     | services.query_processor:process_query:55 - Generated answer with 532 characters
2025-08-07 08:42:25 | INFO     | services.query_processor:process_query:37 - Processing query: 'What are the key points mentioned?...'
2025-08-07 08:42:25 | INFO     | services.vector_store:search:114 - Searching for query: 'the key points mentioned?...' with top_k=2
2025-08-07 08:42:26 | INFO     | services.vector_store:search:136 - Found 2 search results
2025-08-07 08:42:26 | INFO     | services.vector_store:get_relevant_context:166 - Generated context with 2066 characters from 2 chunks
2025-08-07 08:42:28 | INFO     | services.query_processor:process_query:55 - Generated answer with 695 characters
2025-08-07 08:42:28 | INFO     | services.query_processor:process_query:37 - Processing query: 'What information is provided in the document?...'
2025-08-07 08:42:28 | INFO     | services.vector_store:search:114 - Searching for query: 'information provided in the document?...' with top_k=2
2025-08-07 08:42:29 | INFO     | services.vector_store:search:136 - Found 2 search results
2025-08-07 08:42:29 | INFO     | services.vector_store:get_relevant_context:166 - Generated context with 2005 characters from 2 chunks
2025-08-07 08:42:32 | INFO     | services.query_processor:process_query:55 - Generated answer with 892 characters
2025-08-07 08:42:32 | INFO     | main:run_query:127 - Successfully processed 3 questions
2025-08-07 08:46:10 | INFO     | main:run_query:105 - Processing request with 1 questions
2025-08-07 08:46:10 | INFO     | services.document_processor:process_document:38 - Processing document from URL: https://drive.google.com/uc?export=download&id=1l9C5DmyxNgNdXGkhVkJ53V491tat1H1x
2025-08-07 08:46:13 | INFO     | services.document_processor:process_document:61 - Successfully extracted 213274 characters from document
2025-08-07 08:46:13 | INFO     | services.document_processor:chunk_text:205 - Created 248 chunks from text
2025-08-07 08:46:13 | INFO     | services.vector_store:add_documents:64 - Document already processed (hash: 7801d949...), skipping embedding generation
2025-08-07 08:46:13 | INFO     | main:run_query:122 - Processing queries concurrently...
2025-08-07 08:46:13 | INFO     | services.query_processor:process_query:37 - Processing query: 'What is the syllabus of DBMS for 3rd year in module 1?...'
2025-08-07 08:46:13 | INFO     | services.vector_store:search:114 - Searching for query: 'the syllabus of DBMS for 3rd year in module 1?...' with top_k=2
2025-08-07 08:46:14 | INFO     | services.vector_store:search:136 - Found 2 search results
2025-08-07 08:46:14 | INFO     | services.vector_store:get_relevant_context:166 - Generated context with 1853 characters from 2 chunks
2025-08-07 08:46:16 | INFO     | services.query_processor:process_query:55 - Generated answer with 516 characters
2025-08-07 08:46:16 | INFO     | main:run_query:127 - Successfully processed 1 questions
2025-08-07 12:21:52 | INFO     | main:lifespan:35 - Starting LLM-Powered Query-Retrieval System
2025-08-07 12:21:52 | INFO     | services.vector_store:initialize:30 - Initializing vector store...
2025-08-07 12:21:52 | INFO     | services.embedding_service:initialize:34 - Initializing embedding service with provider: gemini
2025-08-07 12:21:53 | INFO     | services.embedding_service:_initialize_gemini:54 - Using Gemini model: models/embedding-001
2025-08-07 12:21:53 | INFO     | services.embedding_service:initialize:39 - Embedding service initialized with dimension: 768
2025-08-07 12:21:53 | INFO     | services.embedding_service:initialize:40 - Using Gemini embeddings for optimal performance
2025-08-07 12:21:54 | INFO     | services.vector_store:_load_index:205 - Loaded vector store with 248 chunks
2025-08-07 12:21:54 | INFO     | services.vector_store:initialize:43 - Vector store initialized with {'provider': 'gemini', 'dimension': 768, 'model': 'models/embedding-001'}
2025-08-07 12:21:54 | INFO     | main:lifespan:43 - System initialized successfully
2025-08-07 12:23:53 | INFO     | main:run_query_internal:122 - Processing request with 3 questions
2025-08-07 12:23:53 | INFO     | services.document_processor:process_document:38 - Processing document from URL: https://drive.google.com/uc?export=download&id=1l9C5DmyxNgNdXGkhVkJ53V491tat1H1x
2025-08-07 12:23:56 | INFO     | services.document_processor:process_document:68 - Successfully extracted 213274 characters from document
2025-08-07 12:23:56 | INFO     | services.document_processor:chunk_text:261 - Created 248 chunks from text
2025-08-07 12:23:56 | INFO     | services.vector_store:add_documents:67 - Adding 248 chunks to vector store
2025-08-07 12:23:56 | INFO     | services.vector_store:add_documents:75 - Generating embeddings...
2025-08-07 12:24:23 | INFO     | services.vector_store:_save_index:186 - Vector store saved to disk
2025-08-07 12:24:23 | INFO     | services.vector_store:add_documents:92 - Successfully added 248 chunks to vector store
2025-08-07 12:24:23 | INFO     | main:run_query_internal:139 - Processing queries concurrently...
2025-08-07 12:24:23 | INFO     | services.query_processor:process_query:37 - Processing query: 'What is the main topic of this document?...'
2025-08-07 12:24:23 | INFO     | services.vector_store:search:114 - Searching for query: 'the main topic of this document?...' with top_k=2
2025-08-07 12:24:24 | INFO     | services.vector_store:search:136 - Found 2 search results
2025-08-07 12:24:24 | INFO     | services.vector_store:get_relevant_context:166 - Generated context with 1646 characters from 2 chunks
2025-08-07 12:24:26 | INFO     | services.query_processor:process_query:55 - Generated answer with 478 characters
2025-08-07 12:24:26 | INFO     | services.query_processor:process_query:37 - Processing query: 'What are the key points mentioned?...'
2025-08-07 12:24:26 | INFO     | services.vector_store:search:114 - Searching for query: 'the key points mentioned?...' with top_k=2
2025-08-07 12:24:26 | INFO     | services.vector_store:search:136 - Found 2 search results
2025-08-07 12:24:26 | INFO     | services.vector_store:get_relevant_context:166 - Generated context with 2066 characters from 2 chunks
2025-08-07 12:24:28 | INFO     | services.query_processor:process_query:55 - Generated answer with 600 characters
2025-08-07 12:24:28 | INFO     | services.query_processor:process_query:37 - Processing query: 'What information is provided in the document?...'
2025-08-07 12:24:28 | INFO     | services.vector_store:search:114 - Searching for query: 'information provided in the document?...' with top_k=2
2025-08-07 12:24:29 | INFO     | services.vector_store:search:136 - Found 2 search results
2025-08-07 12:24:29 | INFO     | services.vector_store:get_relevant_context:166 - Generated context with 2005 characters from 2 chunks
2025-08-07 12:24:32 | INFO     | services.query_processor:process_query:55 - Generated answer with 894 characters
2025-08-07 12:24:32 | INFO     | main:run_query_internal:144 - Successfully processed 3 questions
2025-08-07 12:55:47 | INFO     | main:lifespan:35 - Starting LLM-Powered Query-Retrieval System
2025-08-07 12:55:47 | INFO     | services.vector_store:initialize:30 - Initializing vector store...
2025-08-07 12:55:47 | INFO     | services.embedding_service:initialize:34 - Initializing embedding service with provider: gemini
2025-08-07 12:55:48 | INFO     | services.embedding_service:_initialize_gemini:54 - Using Gemini model: models/embedding-001
2025-08-07 12:55:48 | INFO     | services.embedding_service:initialize:39 - Embedding service initialized with dimension: 768
2025-08-07 12:55:48 | INFO     | services.embedding_service:initialize:40 - Using Gemini embeddings for optimal performance
2025-08-07 12:55:48 | INFO     | services.vector_store:_load_index:205 - Loaded vector store with 248 chunks
2025-08-07 12:55:48 | INFO     | services.vector_store:initialize:43 - Vector store initialized with {'provider': 'gemini', 'dimension': 768, 'model': 'models/embedding-001'}
2025-08-07 12:55:48 | INFO     | main:lifespan:43 - System initialized successfully
2025-08-07 13:09:17 | INFO     | main:run_query_internal:122 - Processing request with 3 questions
2025-08-07 13:09:17 | INFO     | services.document_processor:process_document:38 - Processing document from URL: https://hackrx.blob.core.windows.net/assets/policy.pdf?sv=2023-01-03&st=2025-07-04T09%3A11%3A24Z&se=2027-07-05T09%3A11%3A00Z&sr=b&sp=r&sig=N4a9OU0w0QXO6AOIBiu4bpl7AXvEZogeT%2FjUHNO7HzQ%3D
2025-08-07 13:09:18 | INFO     | services.document_processor:process_document:68 - Successfully extracted 109014 characters from document
2025-08-07 13:09:18 | INFO     | services.document_processor:chunk_text:261 - Created 124 chunks from text
2025-08-07 13:09:18 | INFO     | services.vector_store:add_documents:67 - Adding 124 chunks to vector store
2025-08-07 13:09:18 | INFO     | services.vector_store:add_documents:75 - Generating embeddings...
2025-08-07 13:09:32 | INFO     | services.vector_store:_save_index:186 - Vector store saved to disk
2025-08-07 13:09:32 | INFO     | services.vector_store:add_documents:92 - Successfully added 124 chunks to vector store
2025-08-07 13:09:32 | INFO     | main:run_query_internal:139 - Processing queries concurrently...
2025-08-07 13:09:32 | INFO     | services.query_processor:process_query:37 - Processing query: 'What is the grace period for premium payment?...'
2025-08-07 13:09:32 | INFO     | services.vector_store:search:114 - Searching for query: 'the grace period for premium payment? premium grac...' with top_k=2
2025-08-07 13:09:33 | INFO     | services.vector_store:search:136 - Found 2 search results
2025-08-07 13:09:33 | INFO     | services.vector_store:get_relevant_context:166 - Generated context with 1923 characters from 2 chunks
2025-08-07 13:09:34 | INFO     | services.query_processor:process_query:55 - Generated answer with 164 characters
2025-08-07 13:09:34 | INFO     | services.query_processor:process_query:37 - Processing query: 'What is the waiting period for cataract surgery?...'
2025-08-07 13:09:34 | INFO     | services.vector_store:search:114 - Searching for query: 'the waiting period for cataract surgery? waiting p...' with top_k=2
2025-08-07 13:09:35 | INFO     | services.vector_store:search:136 - Found 2 search results
2025-08-07 13:09:35 | INFO     | services.vector_store:get_relevant_context:166 - Generated context with 2066 characters from 2 chunks
2025-08-07 13:09:36 | INFO     | services.query_processor:process_query:55 - Generated answer with 119 characters
2025-08-07 13:09:36 | INFO     | services.query_processor:process_query:37 - Processing query: 'What is the waiting period for pre-existing diseases?...'
2025-08-07 13:09:36 | INFO     | services.vector_store:search:114 - Searching for query: 'the waiting period for pre-existing diseases? wait...' with top_k=2
2025-08-07 13:09:37 | INFO     | services.vector_store:search:136 - Found 2 search results
2025-08-07 13:09:37 | INFO     | services.vector_store:get_relevant_context:166 - Generated context with 1952 characters from 2 chunks
2025-08-07 13:09:38 | INFO     | services.query_processor:process_query:55 - Generated answer with 337 characters
2025-08-07 13:09:38 | INFO     | main:run_query_internal:144 - Successfully processed 3 questions
2025-08-07 13:16:44 | INFO     | main:run_query_internal:122 - Processing request with 3 questions
2025-08-07 13:16:44 | INFO     | services.document_processor:process_document:38 - Processing document from URL: https://hackrx.blob.core.windows.net/assets/policy.pdf?sv=2023-01-03&st=2025-07-04T09%3A11%3A24Z&se=2027-07-05T09%3A11%3A00Z&sr=b&sp=r&sig=N4a9OU0w0QXO6AOIBiu4bpl7AXvEZogeT%2FjUHNO7HzQ%3D
2025-08-07 13:16:45 | INFO     | services.document_processor:process_document:68 - Successfully extracted 109014 characters from document
2025-08-07 13:16:45 | INFO     | services.document_processor:chunk_text:261 - Created 124 chunks from text
2025-08-07 13:16:45 | INFO     | services.vector_store:add_documents:64 - Document already processed (hash: d913b509...), skipping embedding generation
2025-08-07 13:16:45 | INFO     | main:run_query_internal:139 - Processing queries concurrently...
2025-08-07 13:16:45 | INFO     | services.query_processor:process_query:37 - Processing query: 'What is the grace period for premium payment?...'
2025-08-07 13:16:45 | INFO     | services.vector_store:search:114 - Searching for query: 'the grace period for premium payment? premium grac...' with top_k=2
2025-08-07 13:16:45 | INFO     | services.vector_store:search:136 - Found 2 search results
2025-08-07 13:16:45 | INFO     | services.vector_store:get_relevant_context:166 - Generated context with 1923 characters from 2 chunks
2025-08-07 13:16:47 | INFO     | services.query_processor:process_query:55 - Generated answer with 164 characters
2025-08-07 13:16:47 | INFO     | services.query_processor:process_query:37 - Processing query: 'What is the waiting period for cataract surgery?...'
2025-08-07 13:16:47 | INFO     | services.vector_store:search:114 - Searching for query: 'the waiting period for cataract surgery? waiting p...' with top_k=2
2025-08-07 13:16:48 | INFO     | services.vector_store:search:136 - Found 2 search results
2025-08-07 13:16:48 | INFO     | services.vector_store:get_relevant_context:166 - Generated context with 2066 characters from 2 chunks
2025-08-07 13:16:49 | INFO     | services.query_processor:process_query:55 - Generated answer with 119 characters
2025-08-07 13:16:49 | INFO     | services.query_processor:process_query:37 - Processing query: 'What is the waiting period for pre-existing diseases?...'
2025-08-07 13:16:49 | INFO     | services.vector_store:search:114 - Searching for query: 'the waiting period for pre-existing diseases? wait...' with top_k=2
2025-08-07 13:16:50 | INFO     | services.vector_store:search:136 - Found 2 search results
2025-08-07 13:16:50 | INFO     | services.vector_store:get_relevant_context:166 - Generated context with 1952 characters from 2 chunks
2025-08-07 13:16:51 | INFO     | services.query_processor:process_query:55 - Generated answer with 337 characters
2025-08-07 13:16:51 | INFO     | main:run_query_internal:144 - Successfully processed 3 questions
2025-08-07 13:18:32 | INFO     | main:run_query_internal:122 - Processing request with 10 questions
2025-08-07 13:18:32 | INFO     | services.document_processor:process_document:38 - Processing document from URL: https://hackrx.blob.core.windows.net/assets/policy.pdf?sv=2023-01-03&st=2025-07-04T09%3A11%3A24Z&se=2027-07-05T09%3A11%3A00Z&sr=b&sp=r&sig=N4a9OU0w0QXO6AOIBiu4bpl7AXvEZogeT%2FjUHNO7HzQ%3D
2025-08-07 13:18:33 | INFO     | services.document_processor:process_document:68 - Successfully extracted 109014 characters from document
2025-08-07 13:18:33 | INFO     | services.document_processor:chunk_text:261 - Created 124 chunks from text
2025-08-07 13:18:33 | INFO     | services.vector_store:add_documents:64 - Document already processed (hash: d913b509...), skipping embedding generation
2025-08-07 13:18:33 | INFO     | main:run_query_internal:139 - Processing queries concurrently...
2025-08-07 13:18:33 | INFO     | services.query_processor:process_query:37 - Processing query: 'What is the grace period for premium payment under the National Parivar Mediclaim Plus Policy?...'
2025-08-07 13:18:33 | INFO     | services.vector_store:search:114 - Searching for query: 'the grace period for premium payment under the Nat...' with top_k=2
2025-08-07 13:18:34 | INFO     | services.vector_store:search:136 - Found 2 search results
2025-08-07 13:18:34 | INFO     | services.vector_store:get_relevant_context:166 - Generated context with 2191 characters from 2 chunks
2025-08-07 13:18:35 | INFO     | services.query_processor:process_query:55 - Generated answer with 154 characters
2025-08-07 13:18:35 | INFO     | services.query_processor:process_query:37 - Processing query: 'What is the waiting period for pre-existing diseases (PED) to be covered?...'
2025-08-07 13:18:35 | INFO     | services.vector_store:search:114 - Searching for query: 'the waiting period for pre-existing diseases (PED)...' with top_k=2
2025-08-07 13:18:36 | INFO     | services.vector_store:search:136 - Found 2 search results
2025-08-07 13:18:36 | INFO     | services.vector_store:get_relevant_context:166 - Generated context with 1914 characters from 2 chunks
2025-08-07 13:18:38 | INFO     | services.query_processor:process_query:55 - Generated answer with 458 characters
2025-08-07 13:18:38 | INFO     | services.query_processor:process_query:37 - Processing query: 'Does this policy cover maternity expenses, and what are the conditions?...'
2025-08-07 13:18:38 | INFO     | services.vector_store:search:114 - Searching for query: 'this policy cover maternity expenses, and the cond...' with top_k=2
2025-08-07 13:18:38 | INFO     | services.vector_store:search:136 - Found 2 search results
2025-08-07 13:18:38 | INFO     | services.vector_store:get_relevant_context:166 - Generated context with 1969 characters from 2 chunks
2025-08-07 13:18:41 | INFO     | services.query_processor:process_query:55 - Generated answer with 596 characters
2025-08-07 13:18:41 | INFO     | services.query_processor:process_query:37 - Processing query: 'What is the waiting period for cataract surgery?...'
2025-08-07 13:18:41 | INFO     | services.vector_store:search:114 - Searching for query: 'the waiting period for cataract surgery? waiting p...' with top_k=2
2025-08-07 13:18:41 | INFO     | services.vector_store:search:136 - Found 2 search results
2025-08-07 13:18:41 | INFO     | services.vector_store:get_relevant_context:166 - Generated context with 2066 characters from 2 chunks
2025-08-07 13:18:43 | INFO     | services.query_processor:process_query:55 - Generated answer with 119 characters
2025-08-07 13:18:43 | INFO     | services.query_processor:process_query:37 - Processing query: 'Are the medical expenses for an organ donor covered under this policy?...'
2025-08-07 13:18:43 | INFO     | services.vector_store:search:114 - Searching for query: 'the medical expenses for an organ donor covered un...' with top_k=2
2025-08-07 13:18:43 | INFO     | services.vector_store:search:136 - Found 2 search results
2025-08-07 13:18:43 | INFO     | services.vector_store:get_relevant_context:166 - Generated context with 1999 characters from 2 chunks
2025-08-07 13:18:45 | INFO     | services.query_processor:process_query:55 - Generated answer with 636 characters
2025-08-07 13:18:45 | INFO     | services.query_processor:process_query:37 - Processing query: 'What is the No Claim Discount (NCD) offered in this policy?...'
2025-08-07 13:18:45 | INFO     | services.vector_store:search:114 - Searching for query: 'the No Claim Discount (NCD) offered in this policy...' with top_k=2
2025-08-07 13:18:46 | INFO     | services.vector_store:search:136 - Found 2 search results
2025-08-07 13:18:46 | INFO     | services.vector_store:get_relevant_context:166 - Generated context with 1841 characters from 2 chunks
2025-08-07 13:18:48 | INFO     | services.query_processor:process_query:55 - Generated answer with 333 characters
2025-08-07 13:18:48 | INFO     | services.query_processor:process_query:37 - Processing query: 'Is there a benefit for preventive health check-ups?...'
2025-08-07 13:18:48 | INFO     | services.vector_store:search:114 - Searching for query: 'there a benefit for preventive health check-ups? b...' with top_k=2
2025-08-07 13:18:48 | INFO     | services.vector_store:search:136 - Found 2 search results
2025-08-07 13:18:48 | INFO     | services.vector_store:get_relevant_context:166 - Generated context with 3134 characters from 2 chunks
2025-08-07 13:18:50 | INFO     | services.query_processor:process_query:55 - Generated answer with 391 characters
2025-08-07 13:18:50 | INFO     | services.query_processor:process_query:37 - Processing query: 'How does the policy define a 'Hospital'?...'
2025-08-07 13:18:50 | INFO     | services.vector_store:search:114 - Searching for query: 'the policy define a 'Hospital'? policy hospital...' with top_k=2
2025-08-07 13:18:51 | INFO     | services.vector_store:search:136 - Found 2 search results
2025-08-07 13:18:51 | INFO     | services.vector_store:get_relevant_context:166 - Generated context with 1943 characters from 2 chunks
2025-08-07 13:18:53 | INFO     | services.query_processor:process_query:55 - Generated answer with 676 characters
2025-08-07 13:18:53 | INFO     | services.query_processor:process_query:37 - Processing query: 'What is the extent of coverage for AYUSH treatments?...'
2025-08-07 13:18:53 | INFO     | services.vector_store:search:114 - Searching for query: 'the extent of coverage for AYUSH treatments? cover...' with top_k=2
2025-08-07 13:18:53 | INFO     | services.vector_store:search:136 - Found 2 search results
2025-08-07 13:18:53 | INFO     | services.vector_store:get_relevant_context:166 - Generated context with 1762 characters from 2 chunks
2025-08-07 13:18:55 | INFO     | services.query_processor:process_query:55 - Generated answer with 274 characters
2025-08-07 13:18:55 | INFO     | services.query_processor:process_query:37 - Processing query: 'Are there any sub-limits on room rent and ICU charges for Plan A?...'
2025-08-07 13:18:55 | INFO     | services.vector_store:search:114 - Searching for query: 'there any sub-limits on room rent and ICU charges ...' with top_k=2
2025-08-07 13:18:55 | INFO     | services.vector_store:search:136 - Found 2 search results
2025-08-07 13:18:55 | INFO     | services.vector_store:get_relevant_context:166 - Generated context with 2472 characters from 2 chunks
2025-08-07 13:18:57 | INFO     | services.query_processor:process_query:55 - Generated answer with 285 characters
2025-08-07 13:18:57 | INFO     | main:run_query_internal:144 - Successfully processed 10 questions
2025-08-07 13:22:39 | INFO     | main:run_query_internal:122 - Processing request with 4 questions
2025-08-07 13:22:39 | INFO     | services.document_processor:process_document:38 - Processing document from URL: https://hackrx.blob.core.windows.net/assets/Arogya%20Sanjeevani%20Policy%20-%20CIN%20-%20U10200WB1906GOI001713%201.pdf?sv=2023-01-03&st=2025-07-21T08%3A29%3A02Z&se=2025-09-22T08%3A29%3A00Z&sr=b&sp=r&sig=nzrz1K9Iurt%2BBXom%2FB%2BMPTFMFP3PRnIvEsipAX10Ig4%3D
2025-08-07 13:22:40 | INFO     | services.document_processor:process_document:68 - Successfully extracted 76118 characters from document
2025-08-07 13:22:40 | INFO     | services.document_processor:chunk_text:261 - Created 87 chunks from text
2025-08-07 13:22:40 | INFO     | services.vector_store:add_documents:67 - Adding 87 chunks to vector store
2025-08-07 13:22:40 | INFO     | services.vector_store:add_documents:75 - Generating embeddings...
2025-08-07 13:22:49 | INFO     | services.vector_store:_save_index:186 - Vector store saved to disk
2025-08-07 13:22:49 | INFO     | services.vector_store:add_documents:92 - Successfully added 87 chunks to vector store
2025-08-07 13:22:49 | INFO     | main:run_query_internal:139 - Processing queries concurrently...
2025-08-07 13:22:49 | INFO     | services.query_processor:process_query:37 - Processing query: 'When will my root canal claim of Rs 25,000 be settled?...'
2025-08-07 13:22:49 | INFO     | services.vector_store:search:114 - Searching for query: 'my root canal claim of Rs 25,000 be settled? claim...' with top_k=2
2025-08-07 13:22:50 | INFO     | services.vector_store:search:136 - Found 2 search results
2025-08-07 13:22:50 | INFO     | services.vector_store:get_relevant_context:166 - Generated context with 2051 characters from 2 chunks
2025-08-07 13:22:51 | INFO     | services.query_processor:process_query:55 - Generated answer with 249 characters
2025-08-07 13:22:51 | INFO     | services.query_processor:process_query:37 - Processing query: 'I have done an IVF for Rs 56,000. Is it covered?...'
2025-08-07 13:22:51 | INFO     | services.vector_store:search:114 - Searching for query: 'I have done an IVF for Rs 56,000. it covered?...' with top_k=2
2025-08-07 13:22:52 | INFO     | services.vector_store:search:136 - Found 2 search results
2025-08-07 13:22:52 | INFO     | services.vector_store:get_relevant_context:166 - Generated context with 2031 characters from 2 chunks
2025-08-07 13:22:54 | INFO     | services.query_processor:process_query:55 - Generated answer with 203 characters
2025-08-07 13:22:54 | INFO     | services.query_processor:process_query:37 - Processing query: 'I did a cataract treatment of Rs 100,000. Will you settle the full Rs 100,000?...'
2025-08-07 13:22:54 | INFO     | services.vector_store:search:114 - Searching for query: 'I did a cataract treatment of Rs 100,000. you sett...' with top_k=2
2025-08-07 13:22:54 | INFO     | services.vector_store:search:136 - Found 2 search results
2025-08-07 13:22:54 | INFO     | services.vector_store:get_relevant_context:166 - Generated context with 2330 characters from 2 chunks
2025-08-07 13:22:56 | INFO     | services.query_processor:process_query:55 - Generated answer with 275 characters
2025-08-07 13:22:56 | INFO     | services.query_processor:process_query:37 - Processing query: 'Give me a list of documents to be uploaded for hospitalization for heart surgery....'
2025-08-07 13:22:56 | INFO     | services.vector_store:search:114 - Searching for query: 'Give me a list of documents to be uploaded for hos...' with top_k=2
2025-08-07 13:22:56 | INFO     | services.vector_store:search:136 - Found 2 search results
2025-08-07 13:22:56 | INFO     | services.vector_store:get_relevant_context:166 - Generated context with 2051 characters from 2 chunks
2025-08-07 13:23:00 | INFO     | services.query_processor:process_query:55 - Generated answer with 1239 characters
2025-08-07 13:23:00 | INFO     | main:run_query_internal:144 - Successfully processed 4 questions
2025-08-07 13:23:06 | INFO     | main:run_query_internal:122 - Processing request with 1 questions
2025-08-07 13:23:06 | INFO     | services.document_processor:process_document:38 - Processing document from URL: https://hackrx.blob.core.windows.net/assets/Arogya%20Sanjeevani%20Policy%20-%20CIN%20-%20U10200WB1906GOI001713%201.pdf?sv=2023-01-03&st=2025-07-21T08%3A29%3A02Z&se=2025-09-22T08%3A29%3A00Z&sr=b&sp=r&sig=nzrz1K9Iurt%2BBXom%2FB%2BMPTFMFP3PRnIvEsipAX10Ig4%3D
2025-08-07 13:23:07 | INFO     | services.document_processor:process_document:68 - Successfully extracted 76118 characters from document
2025-08-07 13:23:07 | INFO     | services.document_processor:chunk_text:261 - Created 87 chunks from text
2025-08-07 13:23:07 | INFO     | services.vector_store:add_documents:64 - Document already processed (hash: 2c3b4162...), skipping embedding generation
2025-08-07 13:23:07 | INFO     | main:run_query_internal:139 - Processing queries concurrently...
2025-08-07 13:23:07 | INFO     | services.query_processor:process_query:37 - Processing query: 'I have raised a claim for hospitalization for Rs 200,000 with HDFC, and it's approved. My total expe...'
2025-08-07 13:23:07 | INFO     | services.vector_store:search:114 - Searching for query: 'I have raised a claim for hospitalization for Rs 2...' with top_k=2
2025-08-07 13:23:07 | INFO     | services.vector_store:search:136 - Found 2 search results
2025-08-07 13:23:07 | INFO     | services.vector_store:get_relevant_context:166 - Generated context with 2062 characters from 2 chunks
2025-08-07 13:23:09 | INFO     | services.query_processor:process_query:55 - Generated answer with 455 characters
2025-08-07 13:23:09 | INFO     | main:run_query_internal:144 - Successfully processed 1 questions
2025-08-07 13:23:13 | INFO     | main:run_query_internal:122 - Processing request with 5 questions
2025-08-07 13:23:13 | INFO     | services.document_processor:process_document:38 - Processing document from URL: https://hackrx.blob.core.windows.net/assets/Super_Splendor_(Feb_2023).pdf?sv=2023-01-03&st=2025-07-21T08%3A10%3A00Z&se=2025-09-22T08%3A10%3A00Z&sr=b&sp=r&sig=vhHrl63YtrEOCsAy%2BpVKr20b3ZUo5HMz1lF9%2BJh6LQ0%3D
2025-08-07 13:23:17 | INFO     | services.document_processor:process_document:68 - Successfully extracted 115674 characters from document
2025-08-07 13:23:17 | INFO     | services.document_processor:chunk_text:261 - Created 130 chunks from text
2025-08-07 13:23:17 | INFO     | services.vector_store:add_documents:67 - Adding 130 chunks to vector store
2025-08-07 13:23:17 | INFO     | services.vector_store:add_documents:75 - Generating embeddings...
2025-08-07 13:23:31 | INFO     | services.vector_store:_save_index:186 - Vector store saved to disk
2025-08-07 13:23:31 | INFO     | services.vector_store:add_documents:92 - Successfully added 130 chunks to vector store
2025-08-07 13:23:31 | INFO     | main:run_query_internal:139 - Processing queries concurrently...
2025-08-07 13:23:31 | INFO     | services.query_processor:process_query:37 - Processing query: 'What is the ideal spark plug gap recommeded...'
2025-08-07 13:23:31 | INFO     | services.vector_store:search:114 - Searching for query: 'the ideal spark plug gap recommeded...' with top_k=2
2025-08-07 13:23:32 | INFO     | services.vector_store:search:136 - Found 2 search results
2025-08-07 13:23:32 | INFO     | services.vector_store:get_relevant_context:166 - Generated context with 3206 characters from 2 chunks
2025-08-07 13:23:33 | INFO     | services.query_processor:process_query:55 - Generated answer with 93 characters
2025-08-07 13:23:33 | INFO     | services.query_processor:process_query:37 - Processing query: 'Does this comes in tubeless tyre version...'
2025-08-07 13:23:33 | INFO     | services.vector_store:search:114 - Searching for query: 'this comes in tubeless tyre version...' with top_k=2
2025-08-07 13:23:34 | INFO     | services.vector_store:search:136 - Found 2 search results
2025-08-07 13:23:34 | INFO     | services.vector_store:get_relevant_context:166 - Generated context with 1954 characters from 2 chunks
2025-08-07 13:23:35 | INFO     | services.query_processor:process_query:55 - Generated answer with 140 characters
2025-08-07 13:23:35 | INFO     | services.query_processor:process_query:37 - Processing query: 'Is it compulsoury to have a disc brake...'
2025-08-07 13:23:35 | INFO     | services.vector_store:search:114 - Searching for query: 'it compulsoury to have a disc brake...' with top_k=2
2025-08-07 13:23:36 | INFO     | services.vector_store:search:136 - Found 2 search results
2025-08-07 13:23:36 | INFO     | services.vector_store:get_relevant_context:166 - Generated context with 1506 characters from 2 chunks
2025-08-07 13:23:37 | INFO     | services.query_processor:process_query:55 - Generated answer with 292 characters
2025-08-07 13:23:37 | INFO     | services.query_processor:process_query:37 - Processing query: 'Can I put thums up instead of oil...'
2025-08-07 13:23:37 | INFO     | services.vector_store:search:114 - Searching for query: 'I put thums up instead of oil...' with top_k=2
2025-08-07 13:23:37 | INFO     | services.vector_store:search:136 - Found 2 search results
2025-08-07 13:23:37 | INFO     | services.vector_store:get_relevant_context:166 - Generated context with 2023 characters from 2 chunks
2025-08-07 13:23:38 | INFO     | services.query_processor:process_query:55 - Generated answer with 231 characters
2025-08-07 13:23:38 | INFO     | services.query_processor:process_query:37 - Processing query: 'Give me JS code to generate a random number between 1 and 100...'
2025-08-07 13:23:38 | INFO     | services.vector_store:search:114 - Searching for query: 'Give me JS code to generate a random number betwee...' with top_k=2
2025-08-07 13:23:39 | INFO     | services.vector_store:search:136 - Found 2 search results
2025-08-07 13:23:39 | INFO     | services.vector_store:get_relevant_context:166 - Generated context with 1451 characters from 2 chunks
2025-08-07 13:23:40 | INFO     | services.query_processor:process_query:55 - Generated answer with 303 characters
2025-08-07 13:23:40 | INFO     | main:run_query_internal:144 - Successfully processed 5 questions
2025-08-07 13:23:45 | INFO     | main:run_query_internal:122 - Processing request with 3 questions
2025-08-07 13:23:45 | INFO     | services.document_processor:process_document:38 - Processing document from URL: https://hackrx.blob.core.windows.net/assets/Family%20Medicare%20Policy%20(UIN-%20UIIHLIP22070V042122)%201.pdf?sv=2023-01-03&st=2025-07-22T10%3A17%3A39Z&se=2025-08-23T10%3A17%3A00Z&sr=b&sp=r&sig=dA7BEMIZg3WcePcckBOb4QjfxK%2B4rIfxBs2%2F%2BNwoPjQ%3D
2025-08-07 13:23:46 | INFO     | services.document_processor:process_document:68 - Successfully extracted 96978 characters from document
2025-08-07 13:23:46 | INFO     | services.document_processor:chunk_text:261 - Created 112 chunks from text
2025-08-07 13:23:46 | INFO     | services.vector_store:add_documents:67 - Adding 112 chunks to vector store
2025-08-07 13:23:46 | INFO     | services.vector_store:add_documents:75 - Generating embeddings...
2025-08-07 13:23:56 | INFO     | services.vector_store:_save_index:186 - Vector store saved to disk
2025-08-07 13:23:56 | INFO     | services.vector_store:add_documents:92 - Successfully added 112 chunks to vector store
2025-08-07 13:23:56 | INFO     | main:run_query_internal:139 - Processing queries concurrently...
2025-08-07 13:23:56 | INFO     | services.query_processor:process_query:37 - Processing query: 'Is Non-infective Arthritis covered?...'
2025-08-07 13:23:56 | INFO     | services.vector_store:search:114 - Searching for query: 'Non-infective Arthritis covered?...' with top_k=2
2025-08-07 13:23:57 | INFO     | services.vector_store:search:136 - Found 2 search results
2025-08-07 13:23:57 | INFO     | services.vector_store:get_relevant_context:166 - Generated context with 1833 characters from 2 chunks
2025-08-07 13:23:58 | INFO     | services.query_processor:process_query:55 - Generated answer with 171 characters
2025-08-07 13:23:58 | INFO     | services.query_processor:process_query:37 - Processing query: 'I renewed my policy yesterday, and I have been a customer for the last 6 years. Can I raise a claim ...'
2025-08-07 13:23:58 | INFO     | services.vector_store:search:114 - Searching for query: 'I renewed my policy yesterday, and I have been a c...' with top_k=2
2025-08-07 13:23:58 | INFO     | services.vector_store:search:136 - Found 2 search results
2025-08-07 13:23:58 | INFO     | services.vector_store:get_relevant_context:166 - Generated context with 1752 characters from 2 chunks
2025-08-07 13:23:59 | INFO     | services.query_processor:process_query:55 - Generated answer with 178 characters
2025-08-07 13:23:59 | INFO     | services.query_processor:process_query:37 - Processing query: 'Is abortion covered?...'
2025-08-07 13:23:59 | INFO     | services.vector_store:search:114 - Searching for query: 'abortion covered?...' with top_k=2
2025-08-07 13:24:00 | INFO     | services.vector_store:search:136 - Found 2 search results
2025-08-07 13:24:00 | INFO     | services.vector_store:get_relevant_context:166 - Generated context with 1899 characters from 2 chunks
2025-08-07 13:24:00 | INFO     | services.query_processor:process_query:55 - Generated answer with 202 characters
2025-08-07 13:24:00 | INFO     | main:run_query_internal:144 - Successfully processed 3 questions
2025-08-07 13:24:09 | INFO     | main:run_query_internal:122 - Processing request with 10 questions
2025-08-07 13:24:09 | INFO     | services.document_processor:process_document:38 - Processing document from URL: https://hackrx.blob.core.windows.net/assets/indian_constitution.pdf?sv=2023-01-03&st=2025-07-28T06%3A42%3A00Z&se=2026-11-29T06%3A42%3A00Z&sr=b&sp=r&sig=5Gs%2FOXqP3zY00lgciu4BZjDV5QjTDIx7fgnfdz6Pu24%3D
2025-08-07 13:24:13 | INFO     | services.document_processor:process_document:68 - Successfully extracted 858868 characters from document
2025-08-07 13:24:13 | INFO     | services.document_processor:chunk_text:261 - Created 1025 chunks from text
2025-08-07 13:24:13 | INFO     | services.vector_store:add_documents:67 - Adding 1025 chunks to vector store
2025-08-07 13:24:13 | INFO     | services.vector_store:add_documents:75 - Generating embeddings...
2025-08-07 13:25:45 | INFO     | services.vector_store:_save_index:186 - Vector store saved to disk
2025-08-07 13:25:45 | INFO     | services.vector_store:add_documents:92 - Successfully added 1025 chunks to vector store
2025-08-07 13:25:45 | INFO     | main:run_query_internal:139 - Processing queries concurrently...
2025-08-07 13:25:45 | INFO     | services.query_processor:process_query:37 - Processing query: 'What is the official name of India according to Article 1 of the Constitution?...'
2025-08-07 13:25:45 | INFO     | services.vector_store:search:114 - Searching for query: 'the official name of India according to Article 1 ...' with top_k=2
2025-08-07 13:25:45 | INFO     | services.vector_store:search:136 - Found 2 search results
2025-08-07 13:25:45 | INFO     | services.vector_store:get_relevant_context:166 - Generated context with 1947 characters from 2 chunks
2025-08-07 13:25:46 | INFO     | services.query_processor:process_query:55 - Generated answer with 101 characters
2025-08-07 13:25:46 | INFO     | services.query_processor:process_query:37 - Processing query: 'Which Article guarantees equality before the law and equal protection of laws to all persons?...'
2025-08-07 13:25:46 | INFO     | services.vector_store:search:114 - Searching for query: 'Which Article guarantees equality before the law a...' with top_k=2
2025-08-07 13:25:47 | INFO     | services.vector_store:search:136 - Found 2 search results
2025-08-07 13:25:47 | INFO     | services.vector_store:get_relevant_context:166 - Generated context with 1901 characters from 2 chunks
2025-08-07 13:25:47 | INFO     | services.query_processor:process_query:55 - Generated answer with 128 characters
2025-08-07 13:25:47 | INFO     | services.query_processor:process_query:37 - Processing query: 'What is abolished by Article 17 of the Constitution?...'
2025-08-07 13:25:47 | INFO     | services.vector_store:search:114 - Searching for query: 'abolished by Article 17 of the Constitution?...' with top_k=2
2025-08-07 13:25:48 | INFO     | services.vector_store:search:136 - Found 2 search results
2025-08-07 13:25:49 | INFO     | services.vector_store:get_relevant_context:166 - Generated context with 2025 characters from 2 chunks
2025-08-07 13:25:49 | INFO     | services.query_processor:process_query:55 - Generated answer with 183 characters
2025-08-07 13:25:49 | INFO     | services.query_processor:process_query:37 - Processing query: 'What are the key ideals mentioned in the Preamble of the Constitution of India?...'
2025-08-07 13:25:49 | INFO     | services.vector_store:search:114 - Searching for query: 'the key ideals mentioned in the Preamble of the Co...' with top_k=2
2025-08-07 13:25:50 | INFO     | services.vector_store:search:136 - Found 2 search results
2025-08-07 13:25:50 | INFO     | services.vector_store:get_relevant_context:166 - Generated context with 2490 characters from 2 chunks
2025-08-07 13:25:51 | INFO     | services.query_processor:process_query:55 - Generated answer with 159 characters
2025-08-07 13:25:51 | INFO     | services.query_processor:process_query:37 - Processing query: 'Under which Article can Parliament alter the boundaries, area, or name of an existing State?...'
2025-08-07 13:25:51 | INFO     | services.vector_store:search:114 - Searching for query: 'Under which Article Parliament alter the boundarie...' with top_k=2
2025-08-07 13:25:51 | INFO     | services.vector_store:search:136 - Found 2 search results
2025-08-07 13:25:51 | INFO     | services.vector_store:get_relevant_context:166 - Generated context with 1817 characters from 2 chunks
2025-08-07 13:25:52 | INFO     | services.query_processor:process_query:55 - Generated answer with 301 characters
2025-08-07 13:25:52 | INFO     | services.query_processor:process_query:37 - Processing query: 'According to Article 24, children below what age are prohibited from working in hazardous industries...'
2025-08-07 13:25:52 | INFO     | services.vector_store:search:114 - Searching for query: 'According to Article 24, children below age prohib...' with top_k=2
2025-08-07 13:25:53 | INFO     | services.vector_store:search:136 - Found 2 search results
2025-08-07 13:25:53 | INFO     | services.vector_store:get_relevant_context:166 - Generated context with 2029 characters from 2 chunks
2025-08-07 13:25:53 | INFO     | services.query_processor:process_query:55 - Generated answer with 164 characters
2025-08-07 13:25:53 | INFO     | services.query_processor:process_query:37 - Processing query: 'What is the significance of Article 21 in the Indian Constitution?...'
2025-08-07 13:25:53 | INFO     | services.vector_store:search:114 - Searching for query: 'the significance of Article 21 in the Indian Const...' with top_k=2
2025-08-07 13:25:54 | INFO     | services.vector_store:search:136 - Found 2 search results
2025-08-07 13:25:54 | INFO     | services.vector_store:get_relevant_context:166 - Generated context with 1963 characters from 2 chunks
2025-08-07 13:25:55 | INFO     | services.query_processor:process_query:55 - Generated answer with 165 characters
2025-08-07 13:25:55 | INFO     | services.query_processor:process_query:37 - Processing query: 'Article 15 prohibits discrimination on certain grounds. However, which groups can the State make spe...'
2025-08-07 13:25:55 | INFO     | services.vector_store:search:114 - Searching for query: 'Article 15 prohibits discrimination on certain gro...' with top_k=2
2025-08-07 13:25:55 | INFO     | services.vector_store:search:136 - Found 2 search results
2025-08-07 13:25:55 | INFO     | services.vector_store:get_relevant_context:166 - Generated context with 2645 characters from 2 chunks
2025-08-07 13:25:56 | INFO     | services.query_processor:process_query:55 - Generated answer with 456 characters
2025-08-07 13:25:56 | INFO     | services.query_processor:process_query:37 - Processing query: 'Which Article allows Parliament to regulate the right of citizenship and override previous articles ...'
2025-08-07 13:25:56 | INFO     | services.vector_store:search:114 - Searching for query: 'Which Article allows Parliament to regulate the ri...' with top_k=2
2025-08-07 13:25:57 | INFO     | services.vector_store:search:136 - Found 2 search results
2025-08-07 13:25:57 | INFO     | services.vector_store:get_relevant_context:166 - Generated context with 1723 characters from 2 chunks
2025-08-07 13:25:58 | INFO     | services.query_processor:process_query:55 - Generated answer with 229 characters
2025-08-07 13:25:58 | INFO     | services.query_processor:process_query:37 - Processing query: 'What restrictions can the State impose on the right to freedom of speech under Article 19(2)?...'
2025-08-07 13:25:58 | INFO     | services.vector_store:search:114 - Searching for query: 'restrictions the State impose on the right to free...' with top_k=2
2025-08-07 13:25:58 | INFO     | services.vector_store:search:136 - Found 2 search results
2025-08-07 13:25:58 | INFO     | services.vector_store:get_relevant_context:166 - Generated context with 1918 characters from 2 chunks
2025-08-07 13:25:59 | INFO     | services.query_processor:process_query:55 - Generated answer with 352 characters
2025-08-07 13:25:59 | INFO     | main:run_query_internal:144 - Successfully processed 10 questions
2025-08-07 13:25:59 | INFO     | main:run_query_internal:122 - Processing request with 10 questions
2025-08-07 13:25:59 | INFO     | services.document_processor:process_document:38 - Processing document from URL: https://hackrx.blob.core.windows.net/assets/indian_constitution.pdf?sv=2023-01-03&st=2025-07-28T06%3A42%3A00Z&se=2026-11-29T06%3A42%3A00Z&sr=b&sp=r&sig=5Gs%2FOXqP3zY00lgciu4BZjDV5QjTDIx7fgnfdz6Pu24%3D
2025-08-07 13:26:01 | INFO     | services.document_processor:process_document:68 - Successfully extracted 858868 characters from document
2025-08-07 13:26:01 | INFO     | services.document_processor:chunk_text:261 - Created 1025 chunks from text
2025-08-07 13:26:01 | INFO     | services.vector_store:add_documents:64 - Document already processed (hash: 87184b3f...), skipping embedding generation
2025-08-07 13:26:01 | INFO     | main:run_query_internal:139 - Processing queries concurrently...
2025-08-07 13:26:01 | INFO     | services.query_processor:process_query:37 - Processing query: 'If my car is stolen, what case will it be in law?...'
2025-08-07 13:26:01 | INFO     | services.vector_store:search:114 - Searching for query: 'If my car stolen, case it be in law?...' with top_k=2
2025-08-07 13:26:02 | INFO     | services.vector_store:search:136 - Found 2 search results
2025-08-07 13:26:02 | INFO     | services.vector_store:get_relevant_context:166 - Generated context with 1177 characters from 2 chunks
2025-08-07 13:26:03 | INFO     | services.query_processor:process_query:55 - Generated answer with 311 characters
2025-08-07 13:26:03 | INFO     | services.query_processor:process_query:37 - Processing query: 'If I am arrested without a warrant, is that legal?...'
2025-08-07 13:26:03 | INFO     | services.vector_store:search:114 - Searching for query: 'If I am arrested without a warrant, that legal?...' with top_k=2
2025-08-07 13:26:03 | INFO     | services.vector_store:search:136 - Found 2 search results
2025-08-07 13:26:03 | INFO     | services.vector_store:get_relevant_context:166 - Generated context with 1335 characters from 2 chunks
2025-08-07 13:26:04 | INFO     | services.query_processor:process_query:55 - Generated answer with 281 characters
2025-08-07 13:26:04 | INFO     | services.query_processor:process_query:37 - Processing query: 'If someone denies me a job because of my caste, is that allowed?...'
2025-08-07 13:26:04 | INFO     | services.vector_store:search:114 - Searching for query: 'If someone denies me a job because of my caste, th...' with top_k=2
2025-08-07 13:26:04 | INFO     | services.vector_store:search:136 - Found 2 search results
2025-08-07 13:26:04 | INFO     | services.vector_store:get_relevant_context:166 - Generated context with 1860 characters from 2 chunks
2025-08-07 13:26:06 | INFO     | services.query_processor:process_query:55 - Generated answer with 359 characters
2025-08-07 13:26:06 | INFO     | services.query_processor:process_query:37 - Processing query: 'If the government takes my land for a project, can I stop it?...'
2025-08-07 13:26:06 | INFO     | services.vector_store:search:114 - Searching for query: 'If the government takes my land for a project, I s...' with top_k=2
2025-08-07 13:26:06 | INFO     | services.vector_store:search:136 - Found 2 search results
2025-08-07 13:26:06 | INFO     | services.vector_store:get_relevant_context:166 - Generated context with 2537 characters from 2 chunks
2025-08-07 13:26:07 | INFO     | services.query_processor:process_query:55 - Generated answer with 445 characters
2025-08-07 13:26:07 | INFO     | services.query_processor:process_query:37 - Processing query: 'If my child is forced to work in a factory, is that legal?...'
2025-08-07 13:26:07 | INFO     | services.vector_store:search:114 - Searching for query: 'If my child forced to work in a factory, that lega...' with top_k=2
2025-08-07 13:26:08 | INFO     | services.vector_store:search:136 - Found 2 search results
2025-08-07 13:26:08 | INFO     | services.vector_store:get_relevant_context:166 - Generated context with 2029 characters from 2 chunks
2025-08-07 13:26:09 | INFO     | services.query_processor:process_query:55 - Generated answer with 233 characters
2025-08-07 13:26:09 | INFO     | services.query_processor:process_query:37 - Processing query: 'If I am stopped from speaking at a protest, is that against my rights?...'
2025-08-07 13:26:09 | INFO     | services.vector_store:search:114 - Searching for query: 'If I am stopped from speaking at a protest, that a...' with top_k=2
2025-08-07 13:26:09 | INFO     | services.vector_store:search:136 - Found 2 search results
2025-08-07 13:26:09 | INFO     | services.vector_store:get_relevant_context:166 - Generated context with 2022 characters from 2 chunks
2025-08-07 13:26:10 | INFO     | services.query_processor:process_query:55 - Generated answer with 288 characters
2025-08-07 13:26:10 | INFO     | services.query_processor:process_query:37 - Processing query: 'If a religious place stops me from entering because I'm a woman, is that constitutional?...'
2025-08-07 13:26:10 | INFO     | services.vector_store:search:114 - Searching for query: 'If a religious place stops me from entering becaus...' with top_k=2
2025-08-07 13:26:10 | INFO     | services.vector_store:search:136 - Found 2 search results
2025-08-07 13:26:10 | INFO     | services.vector_store:get_relevant_context:166 - Generated context with 1981 characters from 2 chunks
2025-08-07 13:26:12 | INFO     | services.query_processor:process_query:55 - Generated answer with 661 characters
2025-08-07 13:26:12 | INFO     | services.query_processor:process_query:37 - Processing query: 'If I change my religion, can the government stop me?...'
2025-08-07 13:26:12 | INFO     | services.vector_store:search:114 - Searching for query: 'If I change my religion, the government stop me?...' with top_k=2
2025-08-07 13:26:12 | INFO     | services.vector_store:search:136 - Found 2 search results
2025-08-07 13:26:12 | INFO     | services.vector_store:get_relevant_context:166 - Generated context with 1708 characters from 2 chunks
2025-08-07 13:26:13 | ERROR    | services.query_processor:_generate_answer:189 - Error generating answer with Gemini: 429 You exceeded your current quota, please check your plan and billing details. For more information on this error, head to: https://ai.google.dev/gemini-api/docs/rate-limits. [violations {
  quota_metric: "generativelanguage.googleapis.com/generate_content_free_tier_requests"
  quota_id: "GenerateRequestsPerMinutePerProjectPerModel-FreeTier"
  quota_dimensions {
    key: "model"
    value: "gemini-1.5-flash"
  }
  quota_dimensions {
    key: "location"
    value: "global"
  }
  quota_value: 15
}
, links {
  description: "Learn more about Gemini API quotas"
  url: "https://ai.google.dev/gemini-api/docs/rate-limits"
}
, retry_delay {
  seconds: 46
}
]
2025-08-07 13:26:13 | INFO     | services.query_processor:process_query:55 - Generated answer with 88 characters
2025-08-07 13:26:13 | INFO     | services.query_processor:process_query:37 - Processing query: 'If the police torture someone in custody, what right is being violated?...'
2025-08-07 13:26:13 | INFO     | services.vector_store:search:114 - Searching for query: 'If the police torture someone in custody, right be...' with top_k=2
2025-08-07 13:26:13 | INFO     | services.vector_store:search:136 - Found 2 search results
2025-08-07 13:26:13 | INFO     | services.vector_store:get_relevant_context:166 - Generated context with 1970 characters from 2 chunks
2025-08-07 13:26:14 | ERROR    | services.query_processor:_generate_answer:189 - Error generating answer with Gemini: 429 You exceeded your current quota, please check your plan and billing details. For more information on this error, head to: https://ai.google.dev/gemini-api/docs/rate-limits. [violations {
  quota_metric: "generativelanguage.googleapis.com/generate_content_free_tier_requests"
  quota_id: "GenerateRequestsPerMinutePerProjectPerModel-FreeTier"
  quota_dimensions {
    key: "model"
    value: "gemini-1.5-flash"
  }
  quota_dimensions {
    key: "location"
    value: "global"
  }
  quota_value: 15
}
, links {
  description: "Learn more about Gemini API quotas"
  url: "https://ai.google.dev/gemini-api/docs/rate-limits"
}
, retry_delay {
  seconds: 45
}
]
2025-08-07 13:26:15 | INFO     | services.query_processor:process_query:55 - Generated answer with 88 characters
2025-08-07 13:26:15 | INFO     | services.query_processor:process_query:37 - Processing query: 'If I'm denied admission to a public university because I'm from a backward community, can I do somet...'
2025-08-07 13:26:15 | INFO     | services.vector_store:search:114 - Searching for query: 'If I'm denied admission to a public university bec...' with top_k=2
2025-08-07 13:26:15 | INFO     | services.vector_store:search:136 - Found 2 search results
2025-08-07 13:26:15 | INFO     | services.vector_store:get_relevant_context:166 - Generated context with 1525 characters from 2 chunks
2025-08-07 13:26:16 | ERROR    | services.query_processor:_generate_answer:189 - Error generating answer with Gemini: 429 You exceeded your current quota, please check your plan and billing details. For more information on this error, head to: https://ai.google.dev/gemini-api/docs/rate-limits. [violations {
  quota_metric: "generativelanguage.googleapis.com/generate_content_free_tier_requests"
  quota_id: "GenerateRequestsPerMinutePerProjectPerModel-FreeTier"
  quota_dimensions {
    key: "model"
    value: "gemini-1.5-flash"
  }
  quota_dimensions {
    key: "location"
    value: "global"
  }
  quota_value: 15
}
, links {
  description: "Learn more about Gemini API quotas"
  url: "https://ai.google.dev/gemini-api/docs/rate-limits"
}
, retry_delay {
  seconds: 43
}
]
2025-08-07 13:26:16 | INFO     | services.query_processor:process_query:55 - Generated answer with 88 characters
2025-08-07 13:26:16 | INFO     | main:run_query_internal:144 - Successfully processed 10 questions
2025-08-07 13:26:16 | INFO     | main:run_query_internal:122 - Processing request with 12 questions
2025-08-07 13:26:16 | INFO     | services.document_processor:process_document:38 - Processing document from URL: https://hackrx.blob.core.windows.net/assets/principia_newton.pdf?sv=2023-01-03&st=2025-07-28T07%3A20%3A32Z&se=2026-07-29T07%3A20%3A00Z&sr=b&sp=r&sig=V5I1QYyigoxeUMbnUKsdEaST99F5%2FDfo7wpKg9XXF5w%3D
2025-08-07 13:26:28 | INFO     | services.document_processor:process_document:68 - Successfully extracted 1474390 characters from document
2025-08-07 13:26:29 | INFO     | services.document_processor:chunk_text:261 - Created 1776 chunks from text
2025-08-07 13:26:29 | INFO     | services.vector_store:add_documents:67 - Adding 1776 chunks to vector store
2025-08-07 13:26:29 | INFO     | services.vector_store:add_documents:75 - Generating embeddings...
2025-08-07 13:28:35 | INFO     | services.vector_store:_save_index:186 - Vector store saved to disk
2025-08-07 13:28:35 | INFO     | services.vector_store:add_documents:92 - Successfully added 1776 chunks to vector store
2025-08-07 13:28:35 | INFO     | main:run_query_internal:139 - Processing queries concurrently...
2025-08-07 13:28:35 | INFO     | services.query_processor:process_query:37 - Processing query: 'How does Newton define 'quantity of motion' and how is it distinct from 'force'?...'
2025-08-07 13:28:35 | INFO     | services.vector_store:search:114 - Searching for query: 'Newton define 'quantity of motion' and it distinct...' with top_k=2
2025-08-07 13:28:36 | INFO     | services.vector_store:search:136 - Found 2 search results
2025-08-07 13:28:36 | INFO     | services.vector_store:get_relevant_context:166 - Generated context with 1839 characters from 2 chunks
2025-08-07 13:28:37 | INFO     | services.query_processor:process_query:55 - Generated answer with 358 characters
2025-08-07 13:28:37 | INFO     | services.query_processor:process_query:37 - Processing query: 'According to Newton, what are the three laws of motion and how do they apply in celestial mechanics?...'
2025-08-07 13:28:37 | INFO     | services.vector_store:search:114 - Searching for query: 'According to Newton, the three laws of motion and ...' with top_k=2
2025-08-07 13:28:37 | INFO     | services.vector_store:search:136 - Found 2 search results
2025-08-07 13:28:37 | INFO     | services.vector_store:get_relevant_context:166 - Generated context with 1872 characters from 2 chunks
2025-08-07 13:28:39 | INFO     | services.query_processor:process_query:55 - Generated answer with 514 characters
2025-08-07 13:28:40 | INFO     | services.query_processor:process_query:37 - Processing query: 'How does Newton derive Kepler's Second Law (equal areas in equal times) from his laws of motion and ...'
2025-08-07 13:28:40 | INFO     | services.vector_store:search:114 - Searching for query: 'Newton derive Kepler's Second Law (equal areas in ...' with top_k=2
2025-08-07 13:28:40 | INFO     | services.vector_store:search:136 - Found 2 search results
2025-08-07 13:28:40 | INFO     | services.vector_store:get_relevant_context:166 - Generated context with 1714 characters from 2 chunks
2025-08-07 13:28:42 | INFO     | services.query_processor:process_query:55 - Generated answer with 563 characters
2025-08-07 13:28:42 | INFO     | services.query_processor:process_query:37 - Processing query: 'How does Newton demonstrate that gravity is inversely proportional to the square of the distance bet...'
2025-08-07 13:28:42 | INFO     | services.vector_store:search:114 - Searching for query: 'Newton demonstrate that gravity inversely proporti...' with top_k=2
2025-08-07 13:28:42 | INFO     | services.vector_store:search:136 - Found 2 search results
2025-08-07 13:28:42 | INFO     | services.vector_store:get_relevant_context:166 - Generated context with 1711 characters from 2 chunks
2025-08-07 13:28:43 | INFO     | services.query_processor:process_query:55 - Generated answer with 462 characters
2025-08-07 13:28:43 | INFO     | services.query_processor:process_query:37 - Processing query: 'What is Newton's argument for why gravitational force must act on all masses universally?...'
2025-08-07 13:28:43 | INFO     | services.vector_store:search:114 - Searching for query: 'Newton's argument for gravitational force must act...' with top_k=2
2025-08-07 13:28:44 | INFO     | services.vector_store:search:136 - Found 2 search results
2025-08-07 13:28:44 | INFO     | services.vector_store:get_relevant_context:166 - Generated context with 2042 characters from 2 chunks
2025-08-07 13:28:45 | INFO     | services.query_processor:process_query:55 - Generated answer with 543 characters
2025-08-07 13:28:45 | INFO     | services.query_processor:process_query:37 - Processing query: 'How does Newton explain the perturbation of planetary orbits due to other planets?...'
2025-08-07 13:28:45 | INFO     | services.vector_store:search:114 - Searching for query: 'Newton explain the perturbation of planetary orbit...' with top_k=2
2025-08-07 13:28:46 | INFO     | services.vector_store:search:136 - Found 2 search results
2025-08-07 13:28:46 | INFO     | services.vector_store:get_relevant_context:166 - Generated context with 1886 characters from 2 chunks
2025-08-07 13:28:47 | INFO     | services.query_processor:process_query:55 - Generated answer with 512 characters
2025-08-07 13:28:47 | INFO     | services.query_processor:process_query:37 - Processing query: 'What mathematical tools did Newton use in Principia that were precursors to calculus, and why didn't...'
2025-08-07 13:28:47 | INFO     | services.vector_store:search:114 - Searching for query: 'mathematical tools did Newton use in Principia tha...' with top_k=2
2025-08-07 13:28:47 | INFO     | services.vector_store:search:136 - Found 2 search results
2025-08-07 13:28:47 | INFO     | services.vector_store:get_relevant_context:166 - Generated context with 1823 characters from 2 chunks
2025-08-07 13:28:48 | ERROR    | services.query_processor:_generate_answer:189 - Error generating answer with Gemini: 429 You exceeded your current quota, please check your plan and billing details. For more information on this error, head to: https://ai.google.dev/gemini-api/docs/rate-limits. [violations {
  quota_metric: "generativelanguage.googleapis.com/generate_content_free_tier_requests"
  quota_id: "GenerateRequestsPerDayPerProjectPerModel-FreeTier"
  quota_dimensions {
    key: "model"
    value: "gemini-1.5-flash"
  }
  quota_dimensions {
    key: "location"
    value: "global"
  }
  quota_value: 50
}
, links {
  description: "Learn more about Gemini API quotas"
  url: "https://ai.google.dev/gemini-api/docs/rate-limits"
}
, retry_delay {
  seconds: 11
}
]
2025-08-07 13:28:48 | INFO     | services.query_processor:process_query:55 - Generated answer with 88 characters
2025-08-07 13:28:48 | INFO     | services.query_processor:process_query:37 - Processing query: 'How does Newton use the concept of centripetal force to explain orbital motion?...'
2025-08-07 13:28:48 | INFO     | services.vector_store:search:114 - Searching for query: 'Newton use the concept of centripetal force to exp...' with top_k=2
2025-08-07 13:28:48 | INFO     | services.vector_store:search:136 - Found 2 search results
2025-08-07 13:28:48 | INFO     | services.vector_store:get_relevant_context:166 - Generated context with 1611 characters from 2 chunks
2025-08-07 13:28:49 | ERROR    | services.query_processor:_generate_answer:189 - Error generating answer with Gemini: 429 You exceeded your current quota, please check your plan and billing details. For more information on this error, head to: https://ai.google.dev/gemini-api/docs/rate-limits. [violations {
  quota_metric: "generativelanguage.googleapis.com/generate_content_free_tier_requests"
  quota_id: "GenerateRequestsPerDayPerProjectPerModel-FreeTier"
  quota_dimensions {
    key: "model"
    value: "gemini-1.5-flash"
  }
  quota_dimensions {
    key: "location"
    value: "global"
  }
  quota_value: 50
}
, links {
  description: "Learn more about Gemini API quotas"
  url: "https://ai.google.dev/gemini-api/docs/rate-limits"
}
, retry_delay {
  seconds: 10
}
]
2025-08-07 13:28:49 | INFO     | services.query_processor:process_query:55 - Generated answer with 88 characters
2025-08-07 13:28:49 | INFO     | services.query_processor:process_query:37 - Processing query: 'How does Newton handle motion in resisting media, such as air or fluids?...'
2025-08-07 13:28:49 | INFO     | services.vector_store:search:114 - Searching for query: 'Newton handle motion in resisting media, such as a...' with top_k=2
2025-08-07 13:28:49 | INFO     | services.vector_store:search:136 - Found 2 search results
2025-08-07 13:28:49 | INFO     | services.vector_store:get_relevant_context:166 - Generated context with 1895 characters from 2 chunks
2025-08-07 13:28:49 | ERROR    | services.query_processor:_generate_answer:189 - Error generating answer with Gemini: 429 You exceeded your current quota, please check your plan and billing details. For more information on this error, head to: https://ai.google.dev/gemini-api/docs/rate-limits. [violations {
  quota_metric: "generativelanguage.googleapis.com/generate_content_free_tier_requests"
  quota_id: "GenerateRequestsPerDayPerProjectPerModel-FreeTier"
  quota_dimensions {
    key: "model"
    value: "gemini-1.5-flash"
  }
  quota_dimensions {
    key: "location"
    value: "global"
  }
  quota_value: 50
}
, links {
  description: "Learn more about Gemini API quotas"
  url: "https://ai.google.dev/gemini-api/docs/rate-limits"
}
, retry_delay {
  seconds: 9
}
]
2025-08-07 13:28:49 | INFO     | services.query_processor:process_query:55 - Generated answer with 88 characters
2025-08-07 13:28:49 | INFO     | services.query_processor:process_query:37 - Processing query: 'In what way does Newton's notion of absolute space and time differ from relative motion, and how doe...'
2025-08-07 13:28:49 | INFO     | services.vector_store:search:114 - Searching for query: 'In way Newton's notion of absolute space and time ...' with top_k=2
2025-08-07 13:28:50 | INFO     | services.vector_store:search:136 - Found 2 search results
2025-08-07 13:28:50 | INFO     | services.vector_store:get_relevant_context:166 - Generated context with 2039 characters from 2 chunks
2025-08-07 13:28:50 | ERROR    | services.query_processor:_generate_answer:189 - Error generating answer with Gemini: 429 You exceeded your current quota, please check your plan and billing details. For more information on this error, head to: https://ai.google.dev/gemini-api/docs/rate-limits. [violations {
  quota_metric: "generativelanguage.googleapis.com/generate_content_free_tier_requests"
  quota_id: "GenerateRequestsPerDayPerProjectPerModel-FreeTier"
  quota_dimensions {
    key: "model"
    value: "gemini-1.5-flash"
  }
  quota_dimensions {
    key: "location"
    value: "global"
  }
  quota_value: 50
}
, links {
  description: "Learn more about Gemini API quotas"
  url: "https://ai.google.dev/gemini-api/docs/rate-limits"
}
, retry_delay {
  seconds: 9
}
]
2025-08-07 13:28:50 | INFO     | services.query_processor:process_query:55 - Generated answer with 88 characters
2025-08-07 13:28:50 | INFO     | services.query_processor:process_query:37 - Processing query: 'Who was the grandfather of Isaac Newton?...'
2025-08-07 13:28:50 | INFO     | services.vector_store:search:114 - Searching for query: 'Who was the grandfather of Isaac Newton?...' with top_k=2
2025-08-07 13:28:51 | INFO     | services.vector_store:search:136 - Found 2 search results
2025-08-07 13:28:51 | INFO     | services.vector_store:get_relevant_context:166 - Generated context with 2031 characters from 2 chunks
2025-08-07 13:28:51 | ERROR    | services.query_processor:_generate_answer:189 - Error generating answer with Gemini: 429 You exceeded your current quota, please check your plan and billing details. For more information on this error, head to: https://ai.google.dev/gemini-api/docs/rate-limits. [violations {
  quota_metric: "generativelanguage.googleapis.com/generate_content_free_tier_requests"
  quota_id: "GenerateRequestsPerDayPerProjectPerModel-FreeTier"
  quota_dimensions {
    key: "model"
    value: "gemini-1.5-flash"
  }
  quota_dimensions {
    key: "location"
    value: "global"
  }
  quota_value: 50
}
, links {
  description: "Learn more about Gemini API quotas"
  url: "https://ai.google.dev/gemini-api/docs/rate-limits"
}
, retry_delay {
  seconds: 8
}
]
2025-08-07 13:28:51 | INFO     | services.query_processor:process_query:55 - Generated answer with 88 characters
2025-08-07 13:28:51 | INFO     | services.query_processor:process_query:37 - Processing query: 'Do we know any other descent of Isaac Newton apart from his grandfather?...'
2025-08-07 13:28:51 | INFO     | services.vector_store:search:114 - Searching for query: 'Do we know any other descent of Isaac Newton apart...' with top_k=2
2025-08-07 13:28:52 | INFO     | services.vector_store:search:136 - Found 2 search results
2025-08-07 13:28:52 | INFO     | services.vector_store:get_relevant_context:166 - Generated context with 1933 characters from 2 chunks
2025-08-07 13:28:52 | ERROR    | services.query_processor:_generate_answer:189 - Error generating answer with Gemini: 429 You exceeded your current quota, please check your plan and billing details. For more information on this error, head to: https://ai.google.dev/gemini-api/docs/rate-limits. [violations {
  quota_metric: "generativelanguage.googleapis.com/generate_content_free_tier_requests"
  quota_id: "GenerateRequestsPerDayPerProjectPerModel-FreeTier"
  quota_dimensions {
    key: "model"
    value: "gemini-1.5-flash"
  }
  quota_dimensions {
    key: "location"
    value: "global"
  }
  quota_value: 50
}
, links {
  description: "Learn more about Gemini API quotas"
  url: "https://ai.google.dev/gemini-api/docs/rate-limits"
}
, retry_delay {
  seconds: 6
}
]
2025-08-07 13:28:52 | INFO     | services.query_processor:process_query:55 - Generated answer with 88 characters
2025-08-07 13:28:52 | INFO     | main:run_query_internal:144 - Successfully processed 12 questions
2025-08-07 13:35:36 | INFO     | main:lifespan:48 - Shutting down system
