# Fast startup requirements - core dependencies only
# This file contains only essential dependencies for quick startup

# FastAPI and server
fastapi==0.104.1
uvicorn==0.24.0
python-multipart==0.0.6
pydantic==2.5.0
pydantic-settings==2.1.0

# HTTP clients
requests==2.31.0
httpx==0.25.2

# Document processing
PyMuPDF==1.23.8
python-docx==1.1.0
pypdf2==3.0.1
beautifulsoup4==4.12.2
lxml==4.9.3

# Vector search (lightweight)
faiss-cpu==1.7.4
numpy==1.24.3

# LLM integration
google-generativeai==0.7.2

# Utilities
python-dotenv==1.0.0
loguru==0.7.2
aiofiles==23.2.0

# Note: Heavy ML packages (torch, transformers, sentence-transformers, spacy) 
# are excluded for faster startup. The system will use Gemini embeddings only.
